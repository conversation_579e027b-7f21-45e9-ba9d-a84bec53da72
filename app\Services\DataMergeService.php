<?php

namespace App\Services;

use App\Models\RecommendationLetter;
use App\Models\SystemLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

/**
 * 資料合併服務
 * 
 * 負責向外部系統確認條件、索取文件、PDF合併和壓縮檔生成
 */
class DataMergeService
{
    /**
     * 外部系統API基礎URL
     */
    private string $externalApiBaseUrl;

    /**
     * 合併作業狀態常數
     */
    const STATUS_PENDING = 'pending';
    const STATUS_CHECKING = 'checking';
    const STATUS_FETCHING = 'fetching';
    const STATUS_MERGING = 'merging';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    public function __construct()
    {
        $this->externalApiBaseUrl = config('recommendation.external_api.base_url', 'http://localhost:18001');
    }

    /**
     * 檢查外部系統條件是否滿足
     * 
     * @param array $conditions 檢查條件
     * @return array 檢查結果
     */
    public function checkExternalConditions(array $conditions): array
    {
        try {
            Log::info('開始檢查外部系統條件', ['conditions' => $conditions]);

            // 向外部系統發送條件檢查請求
            $response = Http::timeout(30)->post("{$this->externalApiBaseUrl}/api/v1/recommendation_system/merage_review_pdf", [
                'conditions' => $conditions,
                'timestamp' => now()->toISOString(),
            ]);

            if (!$response->successful()) {
                throw new \Exception("外部系統回應錯誤: {$response->status()}");
            }

            $result = $response->json();

            Log::info('外部系統條件檢查完成', ['result' => $result]);

            return [
                'success' => true,
                'conditions_met' => $result['conditions_met'] ?? false,
                'eligible_count' => $result['eligible_count'] ?? 0,
                'details' => $result['details'] ?? [],
                'message' => $result['message'] ?? '條件檢查完成',
            ];
        } catch (\Exception $e) {
            Log::error('外部系統條件檢查失敗', [
                'conditions' => $conditions,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'conditions_met' => false,
                'eligible_count' => 0,
                'details' => [],
                'message' => '條件檢查失敗: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 從外部系統索取相關文件
     * 
     * @param array $fileRequests 文件請求列表
     * @return array 索取結果
     */
    public function fetchExternalFiles(array $fileRequests): array
    {
        try {
            Log::info('開始從外部系統索取文件', ['requests' => count($fileRequests)]);

            $fetchedFiles = [];
            $failedRequests = [];

            foreach ($fileRequests as $request) {
                try {
                    $response = Http::timeout(60)->post("{$this->externalApiBaseUrl}/api/fetch-recommendation-file", [
                        'applicant_id' => $request['applicant_id'],
                        'exam_id' => $request['exam_id'],
                        'file_type' => $request['file_type'] ?? 'pdf',
                    ]);

                    if ($response->successful()) {
                        $fileData = $response->json();

                        if (isset($fileData['file_content']) && isset($fileData['filename'])) {
                            // 儲存外部文件到臨時目錄
                            $tempPath = $this->storeTemporaryFile(
                                base64_decode($fileData['file_content']),
                                $fileData['filename']
                            );

                            $fetchedFiles[] = [
                                'applicant_id' => $request['applicant_id'],
                                'filename' => $fileData['filename'],
                                'temp_path' => $tempPath,
                                'file_size' => strlen(base64_decode($fileData['file_content'])),
                            ];
                        }
                    } else {
                        $failedRequests[] = [
                            'applicant_id' => $request['applicant_id'],
                            'error' => "HTTP {$response->status()}"
                        ];
                    }
                } catch (\Exception $e) {
                    $failedRequests[] = [
                        'applicant_id' => $request['applicant_id'],
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info('外部文件索取完成', [
                'fetched' => count($fetchedFiles),
                'failed' => count($failedRequests)
            ]);

            return [
                'success' => true,
                'fetched_files' => $fetchedFiles,
                'failed_requests' => $failedRequests,
                'total_requested' => count($fileRequests),
                'total_fetched' => count($fetchedFiles),
            ];
        } catch (\Exception $e) {
            Log::error('外部文件索取失敗', [
                'error' => $e->getMessage(),
                'requests_count' => count($fileRequests)
            ]);

            return [
                'success' => false,
                'fetched_files' => [],
                'failed_requests' => $fileRequests,
                'message' => '文件索取失敗: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 合併推薦函PDF
     * 
     * @param array $localFiles 本地推薦函文件
     * @param array $externalFiles 外部系統文件
     * @return array 合併結果
     */
    public function mergePdfFiles(array $localFiles, array $externalFiles): array
    {
        try {
            Log::info('開始PDF合併作業', [
                'local_files' => count($localFiles),
                'external_files' => count($externalFiles)
            ]);

            $mergedFiles = [];
            $pdfService = new PdfService();

            // 按申請人分組合併
            $groupedFiles = $this->groupFilesByApplicant($localFiles, $externalFiles);

            foreach ($groupedFiles as $applicantId => $files) {
                try {
                    $mergedPdfPath = $pdfService->mergePdfFiles(
                        $files['local'] ?? [],
                        $files['external'] ?? []
                    );

                    $mergedFiles[] = [
                        'applicant_id' => $applicantId,
                        'merged_path' => $mergedPdfPath,
                        'local_count' => count($files['local'] ?? []),
                        'external_count' => count($files['external'] ?? []),
                    ];
                } catch (\Exception $e) {
                    Log::error("申請人 {$applicantId} PDF合併失敗", [
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('PDF合併作業完成', ['merged_count' => count($mergedFiles)]);

            return [
                'success' => true,
                'merged_files' => $mergedFiles,
                'total_merged' => count($mergedFiles),
            ];
        } catch (\Exception $e) {
            Log::error('PDF合併作業失敗', ['error' => $e->getMessage()]);

            return [
                'success' => false,
                'merged_files' => [],
                'message' => 'PDF合併失敗: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 生成壓縮檔
     * 
     * @param array $mergedFiles 合併後的文件列表
     * @return array 壓縮結果
     */
    public function createZipArchive(array $mergedFiles): array
    {
        try {
            $zipFileName = 'merged_recommendations_' . date('Y-m-d_H-i-s') . '.zip';
            $zipPath = storage_path("app/temp/{$zipFileName}");

            // 確保目錄存在
            if (!file_exists(dirname($zipPath))) {
                mkdir(dirname($zipPath), 0755, true);
            }

            $zip = new ZipArchive();
            if ($zip->open($zipPath, ZipArchive::CREATE) !== TRUE) {
                throw new \Exception('無法創建壓縮檔');
            }

            foreach ($mergedFiles as $file) {
                if (file_exists($file['merged_path'])) {
                    $zip->addFile(
                        $file['merged_path'],
                        "applicant_{$file['applicant_id']}_merged.pdf"
                    );
                }
            }

            $zip->close();

            Log::info('壓縮檔創建完成', [
                'zip_path' => $zipPath,
                'file_count' => count($mergedFiles)
            ]);

            return [
                'success' => true,
                'zip_path' => $zipPath,
                'zip_filename' => $zipFileName,
                'file_count' => count($mergedFiles),
                'file_size' => filesize($zipPath),
            ];
        } catch (\Exception $e) {
            Log::error('壓縮檔創建失敗', ['error' => $e->getMessage()]);

            return [
                'success' => false,
                'message' => '壓縮檔創建失敗: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 儲存臨時文件
     */
    private function storeTemporaryFile(string $content, string $filename): string
    {
        $tempDir = storage_path('app/temp/external_files');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $tempPath = $tempDir . '/' . uniqid() . '_' . $filename;
        file_put_contents($tempPath, $content);

        return $tempPath;
    }

    /**
     * 按申請人分組文件
     */
    private function groupFilesByApplicant(array $localFiles, array $externalFiles): array
    {
        $grouped = [];

        foreach ($localFiles as $file) {
            $applicantId = $file['applicant_id'];
            $grouped[$applicantId]['local'][] = $file;
        }

        foreach ($externalFiles as $file) {
            $applicantId = $file['applicant_id'];
            $grouped[$applicantId]['external'][] = $file;
        }

        return $grouped;
    }

    /**
     * 清理臨時文件
     */
    public function cleanupTemporaryFiles(array $filePaths): void
    {
        foreach ($filePaths as $path) {
            if (file_exists($path)) {
                unlink($path);
            }
        }
    }
}
