<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * 建立推薦函系統相關資料表
     * 
     * 此遷移文件統一建立所有推薦函系統相關的資料表
     */
    public function up(): void
    {
        /**
         * 申請人資料表
         * 儲存申請人的基本資訊和第三方系統關聯
         */
        Schema::create('applicants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete(); // 關聯到 users 表
            $table->string('exam_id');                              // 招生代碼 (對應第三方系統)
            $table->integer('exam_year');                           // 招生年度 
            $table->string('external_uid')->nullable()->index();    // 加密的考生 ID (送回API系統解密後查詢用)
            $table->string('phone')->nullable();                    // 聯絡電話
            $table->timestamps();

            // 索引和約束
            $table->unique(['exam_id', 'exam_year', 'user_id']);
        });

        /**
         * 推薦人資料表
         * 儲存推薦人的詳細資訊和登入憑證
         */
        Schema::create('recommenders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->cascadeOnDelete(); // 關聯到 users 表 (可為空，首次邀請時建立)

            $table->string('email')->index();                       // 推薦人電子郵件
            $table->string('name');                                 // 推薦人姓名
            $table->string('title')->nullable();                    // 稱謂/職稱
            $table->string('department')->nullable()->index();      // 所屬部門/機構
            $table->string('phone')->nullable();                    // 聯絡電話

            $table->string('login_token')->nullable()->unique();    // 登入 Token

            $table->string('exam_year')->default(date('Y'))->index();    // 招生年度
            $table->string('exam_id')->index();                     // 招生代碼 (對應招生報名系統)

            $table->timestamp('last_login_at')->nullable();         // 最後登入時間

            $table->timestamp('token_expires_at')->nullable();      // Token 過期時間
            $table->boolean('is_active')->default(true)->index();   // 是否啟用
            $table->timestamps();

            // 索引和約束
            $table->unique(['email', 'exam_year', 'exam_id']); // 同一年度同一招生類別下同一 email 只能有一筆記錄
            $table->index(['department', 'exam_year']);
            $table->index(['is_active', 'exam_year']);
        });

        /**
         * 推薦函資料表
         * 儲存推薦函的詳細資訊、狀態和內容
         */
        Schema::create('recommendation_letters', function (Blueprint $table) {
            $table->id();

            // 關聯資訊
            $table->foreignId('applicant_id')->constrained()->cascadeOnDelete(); // 申請人 ID
            $table->foreignId('recommender_id')->nullable()->constrained()->nullOnDelete(); // 推薦人 ID

            $table->string('exam_id');                              // 招生代碼 (對應第三方系統)
            $table->integer('exam_year');                           // 招生年度 

            /**
             * 考生報名系統的報名流水號
             * 
             * 用於合併作業
             * 階段一：內部合併(將相同流水號的推薦函合併成一個 PDF)
             * 階段二：外部合併(將報名系統的書審資料和推薦函合併成一個 PDF)
             */
            $table->string('external_autono')->index();

            // 推薦人
            $table->string('recommender_email')->index();           // 推薦人電子郵件
            // 推薦人快照資訊（避免推薦人資料異動影響歷史記錄）
            $table->string('recommender_name');                     // 推薦人姓名
            $table->string('recommender_title')->nullable();        // 推薦人職稱
            $table->string('recommender_department')->nullable();   // 推薦人部門
            $table->string('recommender_phone')->nullable();        // 推薦人電話

            // 申請資訊
            $table->string('program_type')->index(); // 招生類別
            $table->string('department_name')->index(); // 報考類組

            // 狀態管理
            // pending: 推薦人尚未回應 < default
            // declined: 推薦人婉拒
            // submitted: 推薦人已提交推薦函
            // withdrawn: 申請人撤回
            $table->enum('status', ['pending',  'declined', 'submitted', 'withdrawn'])
                ->default('pending')->index();

            // 內容和文件
            $table->integer('submit_count')->default(0);            // 提交次數
            $table->string('submission_type')->nullable();          // 提交方式: pdf, questionnaire
            $table->string('pdf_path')->nullable();                 // PDF 檔案路徑
            $table->json('questionnaire_data')->nullable();         // 問卷資料 JSON

            // 時間戳記
            $table->timestamp('submitted_at')->nullable();         // 提交時間
            $table->timestamp('last_reminded_at')->nullable();     // 最後提醒時間
            $table->timestamps();

            // 索引和約束
            $table->unique([
                'applicant_id',
                'recommender_email',
                'department_name',
                'program_type'
            ], 'unique_recommendation_per_group');                  // 確保每個申請人在同一群組只能邀請同一推薦人一次

            $table->index(['department_name', 'program_type']);
            $table->index(['applicant_id', 'status']);
            $table->index(['recommender_email', 'status']);
            $table->index(['status', 'created_at']);
        });

        /**
         * 問卷模板資料表
         * 儲存不同系所和學程的問卷模板
         */
        Schema::create('questionnaire_templates', function (Blueprint $table) {
            $table->id();
            $table->string('department_name')->index(); // 適用系所
            $table->string('program_type')->index(); // 適用學程類型
            $table->string('template_name'); // 模板名稱
            $table->text('description')->nullable(); // 模板描述
            $table->json('questions'); // 問題列表 JSON
            $table->json('pdf_settings')->nullable(); // PDF 生成設定 JSON
            $table->boolean('is_active')->default(true)->index(); // 是否啟用
            $table->integer('sort_order')->default(0); // 排序順序
            $table->timestamps();

            // 索引和約束
            $table->index(['department_name', 'program_type', 'is_active']);
            $table->unique(['template_name', 'department_name', 'program_type']);
        });

        /**
         * 合併文件資料表
         * 儲存合併後的 PDF 文件資訊
         */
        Schema::create('merged_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('applicant_id')->constrained()->cascadeOnDelete(); // 申請人 ID
            $table->enum('document_type', ['application', 'recommendation_bundle']); // 文件類型
            $table->string('department_name')->index(); // 系所名稱
            $table->string('program_type')->index(); // 學程類型
            $table->json('source_files'); // 來源檔案列表 JSON
            $table->string('merged_pdf_path'); // 合併後的 PDF 路徑
            $table->integer('file_size')->nullable(); // 檔案大小 (bytes)
            $table->string('file_hash')->nullable(); // 檔案雜湊值
            $table->timestamp('generated_at'); // 生成時間
            $table->timestamps();

            // 索引
            $table->index(['applicant_id', 'document_type']);
            $table->index(['department_name', 'program_type']);
        });

        /**
         * 郵件記錄資料表
         * 記錄系統發送的所有郵件
         */
        Schema::create('email_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('recommendation_letter_id')->nullable()->constrained()->cascadeOnDelete(); // 關聯推薦函
            $table->string('recipient_email')->index(); // 收件人電子郵件
            $table->string('recipient_name')->nullable(); // 收件人姓名
            $table->string('email_type')->index(); // 郵件類型: invitation, reminder, notification
            $table->string('subject'); // 郵件主旨
            $table->text('content'); // 郵件內容
            $table->unsignedInteger('retry_count')->default(0); // 重試次數
            $table->enum('status', ['pending', 'sent', 'failed', 'bounced'])->default('pending')->index(); // 發送狀態
            $table->text('error_message')->nullable(); // 錯誤訊息
            $table->timestamp('sent_at')->nullable(); // 發送時間
            $table->json('metadata')->nullable(); // 額外資料 JSON
            $table->timestamps();

            // 索引
            $table->index(['recipient_email', 'email_type']);
            $table->index(['status', 'created_at']);
            $table->index(['email_type', 'sent_at']);
        });

        /**
         * 系統設定資料表
         * 儲存系統的各種設定參數
         */
        Schema::create('system_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // 設定鍵值
            $table->text('value'); // 設定值 (JSON格式)
            $table->string('type')->default('string'); // 資料類型 (string, json, boolean, datetime)
            $table->string('category')->default('general'); // 設定分類 (general, timing, security, email)
            $table->string('description')->nullable(); // 設定描述
            $table->boolean('is_active')->default(true); // 是否啟用


            $table->timestamps();

            $table->index(['category', 'is_active']);
        });

        /**
         * 操作日誌資料表
         * 記錄詳細的系統操作日誌
         */
        Schema::create('operation_logs', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // 日誌類型 (login, operation, error, system)
            $table->string('action'); // 操作動作
            $table->text('description'); // 詳細描述
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete(); // 操作用戶
            $table->string('ip_address')->nullable(); // IP地址
            $table->string('user_agent')->nullable(); // 用戶代理
            $table->json('metadata')->nullable(); // 額外資料
            $table->string('level')->default('info'); // 日誌級別 (debug, info, warning, error, critical)
            $table->timestamps();

            $table->index(['type', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['level', 'created_at']);
        });

        /**
         * 登入紀錄（考生、推薦人、管理員）
         */
        Schema::create('login_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable(); // user_id
            $table->ipAddress('ip_address')->nullable(); // IP
            $table->text('user_agent')->nullable(); // User-Agent
            $table->boolean('success')->default(true); // 登入是否成功
            $table->string('failure_reason')->nullable(); // 失敗原因
            $table->timestamp('login_at')->nullable(); // 登入時間
            $table->timestamps();
        });

        /**
         * 系統 API 請求紀錄
         */
        Schema::create('system_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable(); // user_id
            $table->string('action'); // api route name
            $table->json('request_payload')->nullable();
            $table->json('response_payload')->nullable();
            $table->integer('status_code')->nullable();
            $table->timestamps();
        });

        /**
         * PDF合併任務資料表
         * 追蹤PDF合併任務的狀態和進度
         */
        Schema::create('pdf_merge_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('task_id')->unique();         // 任務唯一識別碼
            $table->enum('status', ['processing', 'ready', 'failed', 'expired'])->default('processing'); // 任務狀態
            $table->integer('progress')->default(0);     // 進度百分比 (0-100)
            $table->json('parameters')->nullable();      // 合併參數
            $table->string('download_url')->nullable();  // 下載連結
            $table->string('zip_file_path')->nullable(); // ZIP檔案路徑
            $table->integer('total_files')->default(0);  // 總檔案數
            $table->integer('processed_files')->default(0); // 已處理檔案數
            $table->text('error_message')->nullable();   // 錯誤訊息
            $table->timestamp('expires_at')->nullable(); // 過期時間
            $table->timestamps();

            $table->index(['status', 'created_at']);
            $table->index('task_id');
            $table->index('expires_at');
        });

        /**
         * 使用者條款同意紀錄（可對應任意 user_type）
         */
        Schema::create('user_agreements', function (Blueprint $table) {
            $table->id();
            $table->morphs('user'); // 產生 user_id + user_type，可關聯 Applicant 或 Recommender
            $table->boolean('agree_status'); // 同意狀態
            $table->timestamps();
        });
    }

    /**
     * 回滾遷移
     */
    public function down(): void
    {
        Schema::dropIfExists('user_agreements');
        Schema::dropIfExists('pdf_merge_tasks');
        Schema::dropIfExists('login_logs');
        Schema::dropIfExists('email_logs');
        Schema::dropIfExists('system_logs');
        Schema::dropIfExists('operation_logs');
        Schema::dropIfExists('system_settings');
        Schema::dropIfExists('merged_documents');
        Schema::dropIfExists('questionnaire_templates');
        Schema::dropIfExists('recommendation_letters');
        Schema::dropIfExists('recommenders');
        Schema::dropIfExists('applicants');
    }
};
