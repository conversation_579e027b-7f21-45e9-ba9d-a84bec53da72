import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, Mail, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';
import DataTable from '@/components/admin/DataTable';
import SearchAndFilter from '@/components/admin/SearchAndFilter';
import StatsCards from '@/components/admin/StatsCards';

interface EmailLog {
    id: number;
    recommendation_letter_id: number;
    email_type: string;
    recipient_email: string;
    subject: string;
    status: string;
    sent_at: string;
    error_message?: string;
    created_at: string;
    recommendationLetter?: {
        department_name: string;
        program_type: string;
    };
}

interface EmailLogsProps {
    emailLogs: {
        data: EmailLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function EmailLogs({ emailLogs }: EmailLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: 'Email 管理', href: '/admin/email-logs' },
    ];

    // 狀態徽章
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'sent':
                return (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                        已發送
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                        發送失敗
                    </Badge>
                );
            case 'pending':
                return (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        待發送
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    // Email 類型徽章
    const getTypeBadge = (type: string) => {
        switch (type) {
            case 'invitation':
                return (
                    <Badge variant="outline" className="text-blue-700">
                        邀請信
                    </Badge>
                );
            case 'reminder':
                return (
                    <Badge variant="outline" className="text-orange-700">
                        提醒信
                    </Badge>
                );
            case 'submitted':
                return (
                    <Badge variant="outline" className="text-green-700">
                        完成通知
                    </Badge>
                );
            case 'declined':
                return (
                    <Badge variant="outline" className="text-red-700">
                        婉拒通知
                    </Badge>
                );
            default:
                return <Badge variant="outline">{type}</Badge>;
        }
    };

    // 過濾 Email 日誌
    const filteredLogs = emailLogs.data.filter((log) => {
        const matchesSearch =
            log.recipient_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.recommendationLetter?.department_name?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' || log.status === statusFilter;
        const matchesType = typeFilter === 'all' || log.email_type === typeFilter;

        return matchesSearch && matchesStatus && matchesType;
    });

    // 匯出 Email 日誌
    const handleExportLogs = () => {
        const params = new URLSearchParams();
        if (statusFilter !== 'all') params.append('status', statusFilter);
        if (typeFilter !== 'all') params.append('type', typeFilter);
        if (searchTerm) params.append('search', searchTerm);

        window.open(`/admin/email-logs/export?${params.toString()}`, '_blank');
    };

    // 清理舊日誌
    const handleCleanupLogs = () => {
        if (confirm('確定要清理 30 天前的舊日誌嗎？此操作無法復原。')) {
            router.post(
                '/admin/email-logs/cleanup',
                { days: 30 },
                {
                    onSuccess: () => {
                        alert('舊日誌已清理完成');
                        router.reload();
                    },
                    onError: () => {
                        alert('清理失敗，請重試');
                    },
                },
            );
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="Email 管理" description="查看和管理系統發送的所有 Email 記錄">
            <Head title="Email 管理" />

            <div className="space-y-6 p-6">
                <SearchAndFilter searchTerm={searchTerm} onSearchChange={setSearchTerm} searchPlaceholder="搜尋..." />

                <StatsCards stats={[]} />

                <DataTable data={emailLogs.data} columns={[]} title="" emptyMessage="" />
            </div>
        </AdminLayout>
    );
}
