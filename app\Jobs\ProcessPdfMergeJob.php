<?php

namespace App\Jobs;

use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use App\Services\PdfService;
use App\Services\FilePackagingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * PDF合併處理Job
 * 
 * 處理PDF合併任務，包含書籤插入和檔案打包
 */
class ProcessPdfMergeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任務超時時間（秒）
     */
    public $timeout = 1800; // 30分鐘

    /**
     * 最大重試次數
     */
    public $tries = 3;

    /**
     * 任務ID
     */
    protected string $taskId;

    /**
     * 合併參數
     */
    protected array $parameters;

    /**
     * 創建新的Job實例
     *
     * @param string $taskId 任務ID
     * @param array $parameters 合併參數
     */
    public function __construct(string $taskId, array $parameters)
    {
        $this->taskId = $taskId;
        $this->parameters = $parameters;
    }

    /**
     * 執行Job
     *
     * @return void
     */
    public function handle(): void
    {
        $task = PdfMergeTask::findByTaskId($this->taskId);

        if (!$task) {
            Log::error('PDF合併任務不存在', ['task_id' => $this->taskId]);
            return;
        }

        try {
            Log::info('開始處理PDF合併任務', [
                'task_id' => $this->taskId,
                'parameters' => $this->parameters
            ]);

            // 步驟1: 獲取需要合併的推薦函
            $recommendations = $this->getRecommendationsToMerge();

            if ($recommendations->isEmpty()) {
                throw new \Exception('沒有找到需要合併的推薦函');
            }

            // 步驟2: 按考生分組
            $groupedByApplicant = $this->groupRecommendationsByApplicant($recommendations);

            if (empty($groupedByApplicant)) {
                throw new \Exception('沒有找到需要合併的推薦函或考生資料');
            }

            $task->updateProgress(0, count($groupedByApplicant));

            // 步驟3: 為每個考生合併PDF
            $mergedFiles = [];
            $processedCount = 0;

            foreach ($groupedByApplicant as $applicantId => $applicantRecommendations) {
                try {
                    $mergedPdfPath = $this->mergeApplicantPdfs($applicantRecommendations);

                    if ($mergedPdfPath) {
                        // 獲取autono用於檔案命名
                        $autono = $applicantRecommendations[0]['external_autono'] ?? $applicantId;

                        $mergedFiles[] = [
                            'applicant_id' => $applicantId,
                            'autono' => $autono,
                            'merged_path' => $mergedPdfPath,
                            'recommendation_count' => count($applicantRecommendations)
                        ];
                    }

                    $processedCount++;
                    $task->updateProgress($processedCount);
                } catch (\Exception $e) {
                    Log::error('考生PDF合併失敗', [
                        'applicant_id' => $applicantId,
                        'error' => $e->getMessage()
                    ]);
                    // 繼續處理其他考生
                }
            }

            if (empty($mergedFiles)) {
                throw new \Exception('沒有成功合併任何PDF檔案');
            }

            // 步驟4: 打包成ZIP檔案
            $packagingService = new FilePackagingService();
            $zipFilePath = $packagingService->createZipPackage(
                $this->prepareFilesForPackaging($mergedFiles),
                $this->taskId,
                $this->parameters
            );

            // 步驟5: 清理臨時檔案
            $packagingService->cleanupTemporaryFiles($mergedFiles);

            // 步驟6: 生成下載URL
            $downloadUrl = $this->generateDownloadUrl();

            // 步驟6: 標記任務完成
            $task->markAsReady($zipFilePath, $downloadUrl);

            Log::info('PDF合併任務完成', [
                'task_id' => $this->taskId,
                'merged_files' => count($mergedFiles),
                'zip_file' => $zipFilePath
            ]);
        } catch (\Exception $e) {
            Log::error('PDF合併任務失敗', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $task->markAsFailed($e->getMessage());
        }
    }

    /**
     * 獲取需要合併的推薦函
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getRecommendationsToMerge()
    {
        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_SUBMITTED)
            ->whereNotNull('pdf_path');

        // 根據參數過濾
        if (isset($this->parameters['exam_id'])) {
            $query->where('exam_id', $this->parameters['exam_id']);
        }

        if (isset($this->parameters['exam_year'])) {
            $query->where('exam_year', $this->parameters['exam_year']);
        }

        return $query->with(['applicant', 'recommender'])->get();
    }

    /**
     * 按考生分組推薦函
     *
     * @param \Illuminate\Database\Eloquent\Collection $recommendations
     * @return array
     */
    protected function groupRecommendationsByApplicant($recommendations): array
    {
        return $recommendations->groupBy('applicant_id')->toArray();
    }

    /**
     * 合併單個考生的所有推薦函PDF
     *
     * @param array $recommendations
     * @return string|null
     */
    protected function mergeApplicantPdfs(array $recommendations): ?string
    {
        if (empty($recommendations)) {
            return null;
        }

        $pdfService = new PdfService();
        $applicant = $recommendations[0]['applicant'] ?? null;

        if (!$applicant) {
            throw new \Exception('無法獲取考生資訊');
        }

        // 準備PDF檔案列表
        $pdfFiles = [];
        foreach ($recommendations as $recommendation) {
            // 使用Storage檢查檔案是否存在
            if (Storage::disk('local')->exists($recommendation['pdf_path'])) {
                $pdfPath = Storage::disk('local')->path($recommendation['pdf_path']);

                $recommender = $recommendation['recommender'] ?? null;
                $bookmarkTitle = $this->generateBookmarkTitle($recommender);

                $pdfFiles[] = [
                    'file_path' => $pdfPath,
                    'bookmark_title' => $bookmarkTitle,
                    'recommendation_id' => $recommendation['id']
                ];
            } else {
                Log::warning('PDF檔案不存在', [
                    'recommendation_id' => $recommendation['id'],
                    'pdf_path' => $recommendation['pdf_path']
                ]);
            }
        }

        if (empty($pdfFiles)) {
            throw new \Exception('沒有找到有效的PDF檔案');
        }

        // 使用PdfService合併PDF並插入書籤
        return $pdfService->mergeApplicantPdfsWithBookmarks($pdfFiles, $applicant);
    }

    /**
     * 生成書籤標題
     *
     * @param array|null $recommender
     * @return string
     */
    protected function generateBookmarkTitle(?array $recommender): string
    {
        if (!$recommender) {
            return '推薦函';
        }

        $name = $recommender['name'] ?? '未知推薦人';
        $title = $recommender['title'] ?? '';
        $department = $recommender['department'] ?? '';

        $parts = array_filter([$name, $title, $department]);

        return implode('_', $parts) ?: '推薦函';
    }



    /**
     * 準備檔案供打包服務使用
     *
     * @param array $mergedFiles
     * @return array
     */
    protected function prepareFilesForPackaging(array $mergedFiles): array
    {
        $packageFiles = [];

        foreach ($mergedFiles as $file) {
            // 檢查檔案是否存在
            if (isset($file['merged_path']) && file_exists($file['merged_path'])) {
                // 使用autono作為檔案名稱
                $fileName = ($file['autono'] ?? $file['applicant_id']) . '.pdf';

                $packageFiles[] = [
                    'file_path' => $file['merged_path'],
                    'file_name' => $fileName,
                    'applicant_id' => $file['applicant_id'],
                    'autono' => $file['autono'] ?? $file['applicant_id']
                ];
            } else {
                Log::warning('合併檔案不存在，跳過打包', [
                    'applicant_id' => $file['applicant_id'],
                    'file_path' => $file['merged_path'] ?? 'null'
                ]);
            }
        }

        return $packageFiles;
    }

    /**
     * 生成下載URL
     *
     * @return string
     */
    protected function generateDownloadUrl(): string
    {
        return route('api.pdf-merge.download', [
            'task_id' => $this->taskId,
            'token' => hash('sha256', $this->taskId . config('app.key'))
        ]);
    }

    /**
     * Job失敗時的處理
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        $task = PdfMergeTask::findByTaskId($this->taskId);

        if ($task) {
            $task->markAsFailed($exception->getMessage());
        }

        Log::error('PDF合併Job失敗', [
            'task_id' => $this->taskId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
