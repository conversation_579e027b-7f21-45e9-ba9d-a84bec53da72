import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, LogIn, User, Shield, Users, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';
import DataTable from '@/components/admin/DataTable';
import SearchAndFilter from '@/components/admin/SearchAndFilter';
import StatsCards from '@/components/admin/StatsCards';
import emailLogs from './email-logs';

interface LoginLog {
    id: number;
    user_id: number;
    login_at: string;
    ip_address: string;
    user_agent: string;
    login_method: string;
    session_id?: string;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface LoginLogsProps {
    loginLogs: {
        data: LoginLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function LoginLogs({ loginLogs }: LoginLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [roleFilter, setRoleFilter] = useState('all');
    const [methodFilter, setMethodFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '登入記錄', href: '/admin/login-logs' },
    ];

    // 獲取瀏覽器資訊
    const getBrowserInfo = (userAgent: string) => {
        // 簡單的瀏覽器檢測
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return '未知瀏覽器';
    };

    // 匯出登入記錄
    const handleExportLogs = () => {
        const params = new URLSearchParams();
        if (roleFilter !== 'all') params.append('role', roleFilter);
        if (methodFilter !== 'all') params.append('method', methodFilter);
        if (searchTerm) params.append('search', searchTerm);

        window.open(`/admin/login-logs/export?${params.toString()}`, '_blank');
    };

    // 清理舊記錄
    const handleCleanupLogs = () => {
        if (confirm('確定要清理 90 天前的舊登入記錄嗎？此操作無法復原。')) {
            router.post(
                '/admin/login-logs/cleanup',
                { days: 90 },
                {
                    onSuccess: () => {
                        alert('舊記錄已清理完成');
                        router.reload();
                    },
                    onError: () => {
                        alert('清理失敗，請重試');
                    },
                },
            );
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="登入記錄" description="查看和管理使用者登入記錄和安全日誌">
            <Head title="登入記錄" />

            <div className="space-y-6 p-6">
                <SearchAndFilter searchTerm={searchTerm} onSearchChange={setSearchTerm} searchPlaceholder="搜尋..." />

                <StatsCards stats={[]} />

                <DataTable data={loginLogs.data} columns={[]} title="" emptyMessage="" />
            </div>
        </AdminLayout>
    );
}
