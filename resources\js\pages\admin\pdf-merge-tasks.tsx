import React, { useState, useMemo } from 'react';
import { router } from '@inertiajs/react';

const PdfMergeTasksIndex = ({ tasks, filters: initialFilters, statusOptions }) => {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [creating, setCreating] = useState(false);
    const [searchTimeout, setSearchTimeout] = useState(null);
    const [filters, setFilters] = useState({
        search: initialFilters?.search || '',
        status: initialFilters?.status || ''
    });
    const [newTask, setNewTask] = useState({
        exam_id: '',
        exam_year: null
    });

    // 計算分頁頁碼
    const paginationPages = useMemo(() => {
        const pages = [];
        const current = tasks.current_page;
        const last = tasks.last_page;
        
        // 顯示當前頁前後各2頁
        const start = Math.max(1, current - 2);
        const end = Math.min(last, current + 2);
        
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        
        return pages;
    }, [tasks.current_page, tasks.last_page]);

    // 狀態樣式
    const getStatusClass = (status) => {
        const classes = {
            'processing': 'bg-yellow-100 text-yellow-800',
            'ready': 'bg-green-100 text-green-800',
            'failed': 'bg-red-100 text-red-800',
            'expired': 'bg-gray-100 text-gray-800'
        };
        return classes[status] || 'bg-gray-100 text-gray-800';
    };

    const getStatusText = (status) => {
        return statusOptions[status] || status;
    };

    const getProgressBarClass = (status) => {
        const classes = {
            'processing': 'bg-yellow-500',
            'ready': 'bg-green-500',
            'failed': 'bg-red-500',
            'expired': 'bg-gray-500'
        };
        return classes[status] || 'bg-gray-500';
    };

    // 格式化日期
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('zh-TW');
    };

    // 搜尋防抖
    const debounceSearch = () => {
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
        const timeout = setTimeout(() => {
            applyFilters();
        }, 500);
        setSearchTimeout(timeout);
    };

    // 應用過濾器
    const applyFilters = () => {
        router.get(route('admin.pdf-merge-tasks.index'), filters, {
            preserveState: true,
            preserveScroll: true
        });
    };

    // 清除過濾器
    const clearFilters = () => {
        const newFilters = { search: '', status: '' };
        setFilters(newFilters);
        router.get(route('admin.pdf-merge-tasks.index'), newFilters, {
            preserveState: true,
            preserveScroll: true
        });
    };

    // 刷新任務列表
    const refreshTasks = () => {
        router.reload({ only: ['tasks'] });
    };

    // 跳轉頁面
    const goToPage = (page) => {
        router.get(route('admin.pdf-merge-tasks.index'), {
            ...filters,
            page
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };

    // 查看任務詳情
    const viewTask = (task) => {
        router.visit(route('admin.pdf-merge-tasks.show', task.id));
    };

    // 創建任務
    const createTask = (e) => {
        e.preventDefault();
        setCreating(true);
        
        router.post(route('admin.pdf-merge-tasks.store'), newTask, {
            onSuccess: () => {
                setShowCreateModal(false);
                setNewTask({ exam_id: '', exam_year: null });
                setCreating(false);
            },
            onError: () => {
                setCreating(false);
            }
        });
    };

    // 重試任務
    const retryTask = (task) => {
        if (confirm('確定要重試此任務嗎？')) {
            router.post(route('admin.pdf-merge-tasks.retry', task.id));
        }
    };

    // 取消任務
    const cancelTask = (task) => {
        if (confirm('確定要取消此任務嗎？')) {
            router.post(route('admin.pdf-merge-tasks.cancel', task.id));
        }
    };

    // 下載任務結果
    const downloadTask = (task) => {
        if (task.download_url) {
            window.open(task.download_url, '_blank');
        }
    };

    return (
        <div className="container mx-auto px-4 py-6">
            <div className="bg-white rounded-lg shadow-md">
                {/* 頁面標題 */}
                <div className="px-6 py-4 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <h1 className="text-2xl font-bold text-gray-900">PDF合併任務管理</h1>
                        <button
                            onClick={() => setShowCreateModal(true)}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
                        >
                            創建新任務
                        </button>
                    </div>
                </div>

                {/* 搜尋和過濾 */}
                <div className="px-6 py-4 border-b border-gray-200">
                    <div className="flex flex-wrap gap-4">
                        <div className="flex-1 min-w-64">
                            <input
                                value={filters.search}
                                onChange={(e) => {
                                    setFilters(prev => ({ ...prev, search: e.target.value }));
                                    debounceSearch();
                                }}
                                type="text"
                                placeholder="搜尋任務ID、考試ID或年度..."
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div className="min-w-32">
                            <select
                                value={filters.status}
                                onChange={(e) => {
                                    setFilters(prev => ({ ...prev, status: e.target.value }));
                                    applyFilters();
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">所有狀態</option>
                                {Object.entries(statusOptions).map(([value, label]) => (
                                    <option key={value} value={value}>{label}</option>
                                ))}
                            </select>
                        </div>
                        <button
                            onClick={clearFilters}
                            className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            清除
                        </button>
                        <button
                            onClick={refreshTasks}
                            className="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50"
                        >
                            刷新
                        </button>
                    </div>
                </div>

                {/* 任務列表 */}
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    任務ID
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    考試資訊
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    狀態
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    進度
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    檔案數量
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    創建時間
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {tasks.data.map((task) => (
                                <tr key={task.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-mono text-gray-900">
                                            {task.task_id.substring(0, 20)}...
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">
                                            {task.parameters?.exam_id || 'N/A'} - 
                                            {task.parameters?.exam_year || 'N/A'}年
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusClass(task.status)}`}>
                                            {getStatusText(task.status)}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div 
                                                    className={`h-2 rounded-full transition-all duration-300 ${getProgressBarClass(task.status)}`}
                                                    style={{ width: `${task.progress}%` }}
                                                ></div>
                                            </div>
                                            <span className="text-sm text-gray-600">{task.progress}%</span>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {task.processed_files || 0} / {task.total_files || 0}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {formatDate(task.created_at)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button
                                            onClick={() => viewTask(task)}
                                            className="text-blue-600 hover:text-blue-900"
                                        >
                                            查看
                                        </button>
                                        {(task.status === 'failed' || task.status === 'expired') && (
                                            <button
                                                onClick={() => retryTask(task)}
                                                className="text-green-600 hover:text-green-900"
                                            >
                                                重試
                                            </button>
                                        )}
                                        {task.status === 'processing' && (
                                            <button
                                                onClick={() => cancelTask(task)}
                                                className="text-red-600 hover:text-red-900"
                                            >
                                                取消
                                            </button>
                                        )}
                                        {task.status === 'ready' && task.download_url && (
                                            <button
                                                onClick={() => downloadTask(task)}
                                                className="text-purple-600 hover:text-purple-900"
                                            >
                                                下載
                                            </button>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* 分頁 */}
                {tasks.last_page > 1 && (
                    <div className="px-6 py-4 border-t border-gray-200">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-700">
                                顯示 {tasks.from} 到 {tasks.to} 筆，共 {tasks.total} 筆
                            </div>
                            <div className="flex space-x-1">
                                {paginationPages.map((page) => (
                                    <button
                                        key={page}
                                        onClick={() => goToPage(page)}
                                        className={`px-3 py-2 text-sm rounded-md ${
                                            page === tasks.current_page
                                                ? 'bg-blue-600 text-white'
                                                : 'text-gray-700 hover:bg-gray-100'
                                        }`}
                                    >
                                        {page}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* 創建任務模態框 */}
            {showCreateModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">創建PDF合併任務</h3>
                        
                        <form onSubmit={createTask}>
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700 mb-2">考試ID</label>
                                <input
                                    value={newTask.exam_id}
                                    onChange={(e) => setNewTask(prev => ({ ...prev, exam_id: e.target.value }))}
                                    type="text"
                                    required
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="例如：CS001"
                                />
                            </div>
                            
                            <div className="mb-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">考試年度（民國年）</label>
                                <input
                                    value={newTask.exam_year || ''}
                                    onChange={(e) => setNewTask(prev => ({ ...prev, exam_year: parseInt(e.target.value) || null }))}
                                    type="number"
                                    required
                                    min="100"
                                    max="200"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="例如：113"
                                />
                            </div>
                            
                            <div className="flex justify-end space-x-3">
                                <button
                                    type="button"
                                    onClick={() => setShowCreateModal(false)}
                                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                                >
                                    取消
                                </button>
                                <button
                                    type="submit"
                                    disabled={creating}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                >
                                    {creating ? '創建中...' : '創建任務'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default PdfMergeTasksIndex;
