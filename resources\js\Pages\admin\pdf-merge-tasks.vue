<template>
    <div class="container mx-auto px-4 py-6">
        <div class="bg-white rounded-lg shadow-md">
            <!-- 頁面標題 -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-900">PDF合併任務管理</h1>
                    <button
                        @click="showCreateModal = true"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
                    >
                        創建新任務
                    </button>
                </div>
            </div>

            <!-- 搜尋和過濾 -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input
                            v-model="filters.search"
                            type="text"
                            placeholder="搜尋任務ID、考試ID或年度..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            @input="debounceSearch"
                        />
                    </div>
                    <div class="min-w-32">
                        <select
                            v-model="filters.status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            @change="applyFilters"
                        >
                            <option value="">所有狀態</option>
                            <option v-for="(label, value) in statusOptions" :key="value" :value="value">
                                {{ label }}
                            </option>
                        </select>
                    </div>
                    <button
                        @click="clearFilters"
                        class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        清除
                    </button>
                    <button
                        @click="refreshTasks"
                        class="px-4 py-2 text-blue-600 hover:text-blue-800 border border-blue-300 rounded-md hover:bg-blue-50"
                    >
                        刷新
                    </button>
                </div>
            </div>

            <!-- 任務列表 -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                任務ID
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                考試資訊
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                狀態
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                進度
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                檔案數量
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                創建時間
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="task in tasks.data" :key="task.id" class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-mono text-gray-900">
                                    {{ task.task_id.substring(0, 20) }}...
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ task.parameters?.exam_id || 'N/A' }} - 
                                    {{ task.parameters?.exam_year || 'N/A' }}年
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span :class="getStatusClass(task.status)" class="px-2 py-1 text-xs font-medium rounded-full">
                                    {{ getStatusText(task.status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div 
                                            :class="getProgressBarClass(task.status)"
                                            class="h-2 rounded-full transition-all duration-300"
                                            :style="{ width: task.progress + '%' }"
                                        ></div>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ task.progress }}%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ task.processed_files || 0 }} / {{ task.total_files || 0 }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ formatDate(task.created_at) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button
                                    @click="viewTask(task)"
                                    class="text-blue-600 hover:text-blue-900"
                                >
                                    查看
                                </button>
                                <button
                                    v-if="task.status === 'failed' || task.status === 'expired'"
                                    @click="retryTask(task)"
                                    class="text-green-600 hover:text-green-900"
                                >
                                    重試
                                </button>
                                <button
                                    v-if="task.status === 'processing'"
                                    @click="cancelTask(task)"
                                    class="text-red-600 hover:text-red-900"
                                >
                                    取消
                                </button>
                                <button
                                    v-if="task.status === 'ready' && task.download_url"
                                    @click="downloadTask(task)"
                                    class="text-purple-600 hover:text-purple-900"
                                >
                                    下載
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分頁 -->
            <div v-if="tasks.last_page > 1" class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        顯示 {{ tasks.from }} 到 {{ tasks.to }} 筆，共 {{ tasks.total }} 筆
                    </div>
                    <div class="flex space-x-1">
                        <button
                            v-for="page in paginationPages"
                            :key="page"
                            @click="goToPage(page)"
                            :class="[
                                'px-3 py-2 text-sm rounded-md',
                                page === tasks.current_page
                                    ? 'bg-blue-600 text-white'
                                    : 'text-gray-700 hover:bg-gray-100'
                            ]"
                        >
                            {{ page }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 創建任務模態框 -->
        <div v-if="showCreateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-md">
                <h3 class="text-lg font-medium text-gray-900 mb-4">創建PDF合併任務</h3>
                
                <form @submit.prevent="createTask">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">考試ID</label>
                        <input
                            v-model="newTask.exam_id"
                            type="text"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="例如：CS001"
                        />
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">考試年度（民國年）</label>
                        <input
                            v-model.number="newTask.exam_year"
                            type="number"
                            required
                            min="100"
                            max="200"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="例如：113"
                        />
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button
                            type="button"
                            @click="showCreateModal = false"
                            class="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                            取消
                        </button>
                        <button
                            type="submit"
                            :disabled="creating"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                        >
                            {{ creating ? '創建中...' : '創建任務' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'

export default {
    name: 'PdfMergeTasksIndex',
    props: {
        tasks: Object,
        filters: Object,
        statusOptions: Object
    },
    setup(props) {
        const showCreateModal = ref(false)
        const creating = ref(false)
        const searchTimeout = ref(null)
        
        const filters = reactive({
            search: props.filters?.search || '',
            status: props.filters?.status || ''
        })
        
        const newTask = reactive({
            exam_id: '',
            exam_year: null
        })

        // 計算分頁頁碼
        const paginationPages = computed(() => {
            const pages = []
            const current = props.tasks.current_page
            const last = props.tasks.last_page
            
            // 顯示當前頁前後各2頁
            const start = Math.max(1, current - 2)
            const end = Math.min(last, current + 2)
            
            for (let i = start; i <= end; i++) {
                pages.push(i)
            }
            
            return pages
        })

        // 狀態樣式
        const getStatusClass = (status) => {
            const classes = {
                'processing': 'bg-yellow-100 text-yellow-800',
                'ready': 'bg-green-100 text-green-800',
                'failed': 'bg-red-100 text-red-800',
                'expired': 'bg-gray-100 text-gray-800'
            }
            return classes[status] || 'bg-gray-100 text-gray-800'
        }

        const getStatusText = (status) => {
            return props.statusOptions[status] || status
        }

        const getProgressBarClass = (status) => {
            const classes = {
                'processing': 'bg-yellow-500',
                'ready': 'bg-green-500',
                'failed': 'bg-red-500',
                'expired': 'bg-gray-500'
            }
            return classes[status] || 'bg-gray-500'
        }

        // 格式化日期
        const formatDate = (dateString) => {
            return new Date(dateString).toLocaleString('zh-TW')
        }

        // 搜尋防抖
        const debounceSearch = () => {
            if (searchTimeout.value) {
                clearTimeout(searchTimeout.value)
            }
            searchTimeout.value = setTimeout(() => {
                applyFilters()
            }, 500)
        }

        // 應用過濾器
        const applyFilters = () => {
            router.get(route('admin.pdf-merge-tasks.index'), filters, {
                preserveState: true,
                preserveScroll: true
            })
        }

        // 清除過濾器
        const clearFilters = () => {
            filters.search = ''
            filters.status = ''
            applyFilters()
        }

        // 刷新任務列表
        const refreshTasks = () => {
            router.reload({ only: ['tasks'] })
        }

        // 跳轉頁面
        const goToPage = (page) => {
            router.get(route('admin.pdf-merge-tasks.index'), {
                ...filters,
                page
            }, {
                preserveState: true,
                preserveScroll: true
            })
        }

        // 查看任務詳情
        const viewTask = (task) => {
            router.visit(route('admin.pdf-merge-tasks.show', task.id))
        }

        // 創建任務
        const createTask = () => {
            creating.value = true
            
            router.post(route('admin.pdf-merge-tasks.store'), newTask, {
                onSuccess: () => {
                    showCreateModal.value = false
                    newTask.exam_id = ''
                    newTask.exam_year = null
                    creating.value = false
                },
                onError: () => {
                    creating.value = false
                }
            })
        }

        // 重試任務
        const retryTask = (task) => {
            if (confirm('確定要重試此任務嗎？')) {
                router.post(route('admin.pdf-merge-tasks.retry', task.id))
            }
        }

        // 取消任務
        const cancelTask = (task) => {
            if (confirm('確定要取消此任務嗎？')) {
                router.post(route('admin.pdf-merge-tasks.cancel', task.id))
            }
        }

        // 下載任務結果
        const downloadTask = (task) => {
            if (task.download_url) {
                window.open(task.download_url, '_blank')
            }
        }

        return {
            showCreateModal,
            creating,
            filters,
            newTask,
            paginationPages,
            getStatusClass,
            getStatusText,
            getProgressBarClass,
            formatDate,
            debounceSearch,
            applyFilters,
            clearFilters,
            refreshTasks,
            goToPage,
            viewTask,
            createTask,
            retryTask,
            cancelTask,
            downloadTask
        }
    }
}
</script>
