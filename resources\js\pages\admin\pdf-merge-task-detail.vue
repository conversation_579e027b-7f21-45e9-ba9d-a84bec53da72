<template>
    <div class="container mx-auto px-4 py-6">
        <!-- 返回按鈕 -->
        <div class="mb-6">
            <button
                @click="goBack"
                class="flex items-center text-blue-600 hover:text-blue-800"
            >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                返回任務列表
            </button>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 任務基本資訊 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-start mb-6">
                        <h1 class="text-2xl font-bold text-gray-900">任務詳情</h1>
                        <div class="flex space-x-2">
                            <button
                                v-if="canRetry"
                                @click="retryTask"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md transition-colors"
                            >
                                重試任務
                            </button>
                            <button
                                v-if="canCancel"
                                @click="cancelTask"
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors"
                            >
                                取消任務
                            </button>
                            <button
                                v-if="task.status === 'ready' && task.download_url"
                                @click="downloadTask"
                                class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md transition-colors"
                            >
                                下載結果
                            </button>
                        </div>
                    </div>

                    <!-- 任務狀態卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm text-gray-600 mb-1">任務狀態</div>
                            <div class="flex items-center">
                                <span :class="getStatusClass(task.status)" class="px-3 py-1 text-sm font-medium rounded-full">
                                    {{ getStatusText(task.status) }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm text-gray-600 mb-1">處理進度</div>
                            <div class="flex items-center">
                                <div class="flex-1 bg-gray-200 rounded-full h-3 mr-3">
                                    <div 
                                        :class="getProgressBarClass(task.status)"
                                        class="h-3 rounded-full transition-all duration-300"
                                        :style="{ width: task.progress + '%' }"
                                    ></div>
                                </div>
                                <span class="text-sm font-medium">{{ task.progress }}%</span>
                            </div>
                        </div>
                    </div>

                    <!-- 任務詳細資訊 -->
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">任務ID</label>
                                <div class="text-sm font-mono bg-gray-100 p-2 rounded border">
                                    {{ task.task_id }}
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">考試資訊</label>
                                <div class="text-sm bg-gray-100 p-2 rounded border">
                                    {{ task.parameters?.exam_id || 'N/A' }} - {{ task.parameters?.exam_year || 'N/A' }}年
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">總檔案數</label>
                                <div class="text-lg font-semibold text-blue-600">{{ task.total_files || 0 }}</div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">已處理檔案</label>
                                <div class="text-lg font-semibold text-green-600">{{ task.processed_files || 0 }}</div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">剩餘檔案</label>
                                <div class="text-lg font-semibold text-orange-600">
                                    {{ (task.total_files || 0) - (task.processed_files || 0) }}
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">創建時間</label>
                                <div class="text-sm text-gray-900">{{ formatDate(task.created_at) }}</div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">更新時間</label>
                                <div class="text-sm text-gray-900">{{ formatDate(task.updated_at) }}</div>
                            </div>
                        </div>

                        <div v-if="task.expires_at">
                            <label class="block text-sm font-medium text-gray-700 mb-1">過期時間</label>
                            <div class="text-sm text-gray-900">{{ formatDate(task.expires_at) }}</div>
                        </div>

                        <div v-if="task.error_message" class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <label class="block text-sm font-medium text-red-700 mb-2">錯誤訊息</label>
                            <div class="text-sm text-red-600 font-mono">{{ task.error_message }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 側邊欄 -->
            <div class="space-y-6">
                <!-- 快速操作 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
                    <div class="space-y-3">
                        <button
                            @click="refreshTask"
                            class="w-full text-left px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                        >
                            🔄 刷新任務狀態
                        </button>
                        
                        <button
                            v-if="task.status === 'ready'"
                            @click="viewLogs"
                            class="w-full text-left px-4 py-2 text-green-600 hover:bg-green-50 rounded-md transition-colors"
                        >
                            📋 查看處理日誌
                        </button>
                        
                        <button
                            @click="deleteTask"
                            class="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                        >
                            🗑️ 刪除任務
                        </button>
                    </div>
                </div>

                <!-- 相關推薦函 -->
                <div v-if="recommendations && recommendations.length > 0" class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">相關推薦函</h3>
                    <div class="space-y-3">
                        <div
                            v-for="rec in recommendations"
                            :key="rec.id"
                            class="border border-gray-200 rounded-lg p-3"
                        >
                            <div class="text-sm font-medium text-gray-900">
                                {{ rec.applicant?.name || '未知考生' }}
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                推薦人: {{ rec.recommender_name || 'N/A' }}
                            </div>
                            <div class="text-xs text-gray-500">
                                狀態: {{ rec.status }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任務統計 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">任務統計</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">處理效率</span>
                            <span class="text-sm font-medium">
                                {{ Math.round((task.processed_files || 0) / (task.total_files || 1) * 100) }}%
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">執行時間</span>
                            <span class="text-sm font-medium">
                                {{ getExecutionTime() }}
                            </span>
                        </div>
                        
                        <div v-if="task.zip_file_path" class="flex justify-between">
                            <span class="text-sm text-gray-600">輸出檔案</span>
                            <span class="text-sm font-medium text-green-600">已生成</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, computed } from 'vue'
import { router } from '@inertiajs/vue3'

export default {
    name: 'PdfMergeTaskDetail',
    props: {
        task: Object,
        recommendations: Array,
        canRetry: Boolean,
        canCancel: Boolean
    },
    setup(props) {
        // 狀態樣式
        const getStatusClass = (status) => {
            const classes = {
                'processing': 'bg-yellow-100 text-yellow-800',
                'ready': 'bg-green-100 text-green-800',
                'failed': 'bg-red-100 text-red-800',
                'expired': 'bg-gray-100 text-gray-800'
            }
            return classes[status] || 'bg-gray-100 text-gray-800'
        }

        const getStatusText = (status) => {
            const statusMap = {
                'processing': '處理中',
                'ready': '完成',
                'failed': '失敗',
                'expired': '已過期'
            }
            return statusMap[status] || status
        }

        const getProgressBarClass = (status) => {
            const classes = {
                'processing': 'bg-yellow-500',
                'ready': 'bg-green-500',
                'failed': 'bg-red-500',
                'expired': 'bg-gray-500'
            }
            return classes[status] || 'bg-gray-500'
        }

        // 格式化日期
        const formatDate = (dateString) => {
            return new Date(dateString).toLocaleString('zh-TW')
        }

        // 計算執行時間
        const getExecutionTime = () => {
            if (!props.task.created_at) return 'N/A'
            
            const start = new Date(props.task.created_at)
            const end = props.task.updated_at ? new Date(props.task.updated_at) : new Date()
            const diff = Math.floor((end - start) / 1000) // 秒
            
            if (diff < 60) return `${diff}秒`
            if (diff < 3600) return `${Math.floor(diff / 60)}分鐘`
            return `${Math.floor(diff / 3600)}小時`
        }

        // 操作方法
        const goBack = () => {
            router.visit(route('admin.pdf-merge-tasks.index'))
        }

        const refreshTask = () => {
            router.reload({ only: ['task'] })
        }

        const retryTask = () => {
            if (confirm('確定要重試此任務嗎？')) {
                router.post(route('admin.pdf-merge-tasks.retry', props.task.id))
            }
        }

        const cancelTask = () => {
            if (confirm('確定要取消此任務嗎？')) {
                router.post(route('admin.pdf-merge-tasks.cancel', props.task.id))
            }
        }

        const downloadTask = () => {
            if (props.task.download_url) {
                window.open(props.task.download_url, '_blank')
            }
        }

        const deleteTask = () => {
            if (confirm('確定要刪除此任務嗎？此操作無法復原。')) {
                router.delete(route('admin.pdf-merge-tasks.destroy', props.task.id))
            }
        }

        const viewLogs = () => {
            // 這裡可以實現查看日誌的功能
            alert('日誌查看功能開發中...')
        }

        return {
            getStatusClass,
            getStatusText,
            getProgressBarClass,
            formatDate,
            getExecutionTime,
            goBack,
            refreshTask,
            retryTask,
            cancelTask,
            downloadTask,
            deleteTask,
            viewLogs
        }
    }
}
</script>
