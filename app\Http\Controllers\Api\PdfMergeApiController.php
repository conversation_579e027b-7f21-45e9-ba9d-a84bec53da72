<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessPdfMergeJob;
use App\Models\PdfMergeTask;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

/**
 * PDF合併API控制器
 * 
 * 提供外部系統調用的PDF合併功能API
 */
class PdfMergeApiController extends Controller
{
    /**
     * 啟動PDF合併任務
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function startMerge(Request $request): JsonResponse
    {
        try {
            // 驗證請求參數
            $validator = Validator::make($request->all(), [
                'exam_id' => 'required|string|max:50',
                'exam_year' => 'required|integer|min:100|max:200',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 準備合併參數
            $parameters = array_filter([
                'exam_id' => $request->input('exam_id'),
                'exam_year' => $request->input('exam_year'),
                'requested_at' => now()->toISOString(),
                'client_ip' => $request->ip(),
            ]);

            // 創建合併任務
            $task = PdfMergeTask::createTask($parameters);

            // 派發背景任務
            ProcessPdfMergeJob::dispatch($task->task_id, $parameters);

            Log::info('PDF合併任務已啟動', [
                'task_id' => $task->task_id,
                'parameters' => $parameters,
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'processing',
                'task_id' => $task->task_id,
                'message' => '合併任務已啟動，請使用task_id查詢進度'
            ]);
        } catch (\Exception $e) {
            Log::error('啟動PDF合併任務失敗', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '啟動合併任務失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 查詢合併任務狀態
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getMergeStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $taskId = $request->input('task_id');
            $task = PdfMergeTask::findByTaskId($taskId);

            if (!$task) {
                return response()->json([
                    'status' => 'error',
                    'message' => '找不到指定的任務'
                ], 404);
            }

            // 檢查任務是否過期
            if ($task->isExpired()) {
                $task->update(['status' => PdfMergeTask::STATUS_EXPIRED]);

                return response()->json([
                    'status' => 'expired',
                    'message' => '任務已過期',
                    'task_id' => $taskId
                ]);
            }

            // 根據任務狀態返回不同的響應
            $response = [
                'status' => $task->status,
                'task_id' => $taskId,
                'progress' => $task->progress,
                'created_at' => $task->created_at->toISOString(),
            ];

            switch ($task->status) {
                case PdfMergeTask::STATUS_PROCESSING:
                    $response['message'] = '任務處理中';
                    $response['processed_files'] = $task->processed_files;
                    $response['total_files'] = $task->total_files;
                    break;

                case PdfMergeTask::STATUS_READY:
                    if ($task->isReady()) {
                        $response['message'] = '任務完成，可以下載';
                        $response['download_url'] = $task->download_url;
                        $response['expires_at'] = $task->expires_at?->toISOString();
                    } else {
                        $response['status'] = 'error';
                        $response['message'] = '檔案不存在或已損壞';
                    }
                    break;

                case PdfMergeTask::STATUS_FAILED:
                    $response['message'] = '任務處理失敗';
                    $response['error'] = $task->error_message;
                    break;

                case PdfMergeTask::STATUS_EXPIRED:
                    $response['message'] = '任務已過期';
                    break;

                default:
                    $response['message'] = '未知狀態';
                    break;
            }

            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('查詢PDF合併任務狀態失敗', [
                'error' => $e->getMessage(),
                'task_id' => $request->input('task_id'),
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '查詢任務狀態失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 下載合併後的PDF檔案
     * 
     * @param Request $request
     * @return Response|JsonResponse
     */
    public function downloadMergedFile(Request $request): Response|JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string',
                'token' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $taskId = $request->input('task_id');
            $token = $request->input('token');

            // 驗證token
            $expectedToken = hash('sha256', $taskId . config('app.key'));
            if (!hash_equals($expectedToken, $token)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '無效的下載token'
                ], 403);
            }

            $task = PdfMergeTask::findByTaskId($taskId);

            if (!$task) {
                return response()->json([
                    'status' => 'error',
                    'message' => '找不到指定的任務'
                ], 404);
            }

            if (!$task->isReady()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '檔案尚未準備好或已過期'
                ], 400);
            }

            $filePath = storage_path('app/' . $task->zip_file_path);

            if (!file_exists($filePath)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '檔案不存在'
                ], 404);
            }

            Log::info('PDF合併檔案下載', [
                'task_id' => $taskId,
                'file_path' => $task->zip_file_path,
                'client_ip' => $request->ip()
            ]);

            return response()->download($filePath, basename($task->zip_file_path), [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . basename($task->zip_file_path) . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('下載PDF合併檔案失敗', [
                'error' => $e->getMessage(),
                'task_id' => $request->input('task_id'),
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '下載檔案失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 清理過期任務（內部API）
     * 
     * @return JsonResponse
     */
    public function cleanupExpiredTasks(): JsonResponse
    {
        try {
            $cleanedCount = PdfMergeTask::cleanupExpiredTasks();

            Log::info('PDF合併任務清理完成', [
                'cleaned_count' => $cleanedCount
            ]);

            return response()->json([
                'status' => 'success',
                'message' => "已清理 {$cleanedCount} 個過期任務",
                'cleaned_count' => $cleanedCount
            ]);
        } catch (\Exception $e) {
            Log::error('清理過期任務失敗', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '清理過期任務失敗'
            ], 500);
        }
    }
}
