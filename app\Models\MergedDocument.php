<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

/**
 * 合併文件模型
 * 
 * 管理合併後的 PDF 文件，包括申請文件和推薦函合併
 */
class MergedDocument extends Model
{
    use HasFactory;

    /**
     * 文件類型常數
     */
    const TYPE_APPLICATION = 'application';           // 申請文件
    const TYPE_RECOMMENDATION_BUNDLE = 'recommendation_bundle'; // 推薦函合併

    /**
     * 可批量賦值的屬性
     * 
     * @var array<string>
     */
    protected $fillable = [
        'applicant_id',      // 申請人 ID
        'document_type',     // 文件類型
        'department_name',   // 系所名稱
        'program_type',      // 學程類型
        'source_files',      // 來源檔案列表
        'merged_pdf_path',   // 合併後的 PDF 路徑
        'file_size',         // 檔案大小
        'file_hash',         // 檔案雜湊值
        'generated_at',      // 生成時間
    ];

    /**
     * 屬性類型轉換
     * 
     * @var array<string, string>
     */
    protected $casts = [
        'source_files' => 'array',
        'generated_at' => 'datetime',
    ];

    /**
     * 取得合併文件關聯的申請人
     * 
     * @return BelongsTo
     */
    public function applicant(): BelongsTo
    {
        return $this->belongsTo(Applicant::class);
    }

    /**
     * 檢查是否為申請文件類型
     * 
     * @return bool
     */
    public function isApplicationDocument(): bool
    {
        return $this->document_type === self::TYPE_APPLICATION;
    }

    /**
     * 檢查是否為推薦函合併文件
     * 
     * @return bool
     */
    public function isRecommendationBundle(): bool
    {
        return $this->document_type === self::TYPE_RECOMMENDATION_BUNDLE;
    }

    /**
     * 取得 PDF 檔案的完整 URL
     * 
     * @return string|null
     */
    public function getPdfUrlAttribute(): ?string
    {
        return $this->merged_pdf_path ? Storage::url($this->merged_pdf_path) : null;
    }

    /**
     * 取得檔案大小的人類可讀格式
     * 
     * @return string
     */
    public function getFileSizeHumanAttribute(): string
    {
        if (!$this->file_size) {
            return '未知大小';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unitIndex = 0;

        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }

        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * 取得文件類型的中文顯示
     * 
     * @return string
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->document_type) {
            self::TYPE_APPLICATION => '申請文件',
            self::TYPE_RECOMMENDATION_BUNDLE => '推薦函合併',
            default => '未知類型',
        };
    }

    /**
     * 取得文件的顯示名稱
     * 
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->department_name}_{$this->program_type}_{$this->type_display}";
    }

    /**
     * 檢查檔案是否存在
     * 
     * @return bool
     */
    public function fileExists(): bool
    {
        return $this->merged_pdf_path && Storage::exists($this->merged_pdf_path);
    }

    /**
     * 刪除關聯的檔案
     * 
     * @return bool
     */
    public function deleteFile(): bool
    {
        if ($this->merged_pdf_path && Storage::exists($this->merged_pdf_path)) {
            return Storage::delete($this->merged_pdf_path);
        }
        
        return true;
    }

    /**
     * 計算並更新檔案雜湊值
     * 
     * @return bool
     */
    public function updateFileHash(): bool
    {
        if (!$this->fileExists()) {
            return false;
        }

        $content = Storage::get($this->merged_pdf_path);
        $hash = hash('sha256', $content);
        
        return $this->update(['file_hash' => $hash]);
    }

    /**
     * 驗證檔案完整性
     * 
     * @return bool
     */
    public function verifyFileIntegrity(): bool
    {
        if (!$this->file_hash || !$this->fileExists()) {
            return false;
        }

        $content = Storage::get($this->merged_pdf_path);
        $currentHash = hash('sha256', $content);
        
        return $currentHash === $this->file_hash;
    }

    /**
     * 模型刪除時的事件處理
     * 
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($mergedDocument) {
            // 刪除模型時同時刪除關聯的檔案
            $mergedDocument->deleteFile();
        });
    }
}
