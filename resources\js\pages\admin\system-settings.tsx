import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { router, useForm } from '@inertiajs/react';
import { Save, AlertTriangle, RefreshCw, Settings, Download, Wifi, WifiOff, Power, PowerOff } from 'lucide-react';
import { useState } from 'react';

interface SystemSetting {
    id: number;
    key: string;
    value: string;
    type: string;
    category: string;
    description: string;
    is_active: boolean;
}

interface SystemSettingsProps {
    settings: {
        timing: SystemSetting[];
        general: SystemSetting[];
        security: SystemSetting[];
        email: SystemSetting[];
    };
    system_status: {
        is_maintenance_mode: boolean;
        reminder_cooldown_hours: number;
        auto_timeout_days: number;
    };
}

export default function SystemSettings({ settings, system_status }: SystemSettingsProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);
    const [isCheckingApi, setIsCheckingApi] = useState(false);
    const [apiStatus, setApiStatus] = useState<{ success: boolean; message: string } | null>(null);

    const { data, setData, processing, errors } = useForm({
        reminder_cooldown: settings?.timing.find((setting) => setting.key === 'reminder.cooldown_hours')?.value || '24',
        auto_timeout: settings?.timing.find((setting) => setting.key === 'auto.timeout_days')?.value || '0',
    });

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統設定', href: '/admin/system-settings' },
    ];

    const handleSaveSettings = () => {
        // 使用 PUT 方法更新設定
        router.put('/admin/system-settings', data, {
            onSuccess: () => {
                alert('設定已儲存');
            },
            onError: (errors) => {
                console.error('儲存失敗:', errors);
                alert('儲存失敗，請檢查輸入資料');
            },
        });
    };

    // 切換維護模式
    const toggleMaintenanceMode = (enabled: boolean) => {
        setIsLoading(true);

        router.post(
            '/admin/system-settings/toggle-maintenance-mode',
            { enabled },
            {
                onFinish: () => setIsLoading(false),
                onSuccess: () => {
                    // 重新載入頁面以更新狀態
                    router.reload();
                },
                onError: () => {
                    alert('切換維護模式失敗，請重試');
                },
            },
        );
    };

    // 同步外部資料
    const syncExternalData = () => {
        setIsSyncing(true);

        router.post(
            '/admin/system-settings/sync-external-data',
            {},
            {
                onFinish: () => setIsSyncing(false),
                onSuccess: (page) => {
                    alert('同步成功！');
                    router.reload();
                },
                onError: () => {
                    alert('同步失敗，請檢查外部API連線狀態');
                },
            },
        );
    };

    // 檢查API連線
    const checkApiConnection = () => {
        setIsCheckingApi(true);

        router.get(
            '/admin/system-settings/check-api-connection',
            {},
            {
                onFinish: () => setIsCheckingApi(false),
                onSuccess: (page: any) => {
                    const response = page.props.flash?.api_status;
                    if (response) {
                        setApiStatus(response);
                    }
                },
                onError: () => {
                    setApiStatus({
                        success: false,
                        message: '檢查API連線失敗',
                    });
                },
            },
        );
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="系統設定" description="管理系統的各項參數和配置">
            <Head title="系統設定" />

            <div className="space-y-6 p-6">
                {/* 系統狀態概覽 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-base">系統狀態概覽</CardTitle>
                        <CardDescription className="text-xs">目前系統的運行狀態</CardDescription>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <div className="col-span-full rounded-lg border bg-gray-50 p-4">
                            <div className="space-y-2">
                                <div className="flex justify-between text-xs">
                                    <span className="text-gray-600">提醒冷卻時間</span>
                                    <span className="font-medium">{system_status.reminder_cooldown_hours} 小時</span>
                                </div>
                                <div className="flex justify-between text-xs">
                                    <span className="text-gray-600">自動超時天數</span>
                                    <span className="font-medium">{system_status.auto_timeout_days} 天</span>
                                </div>
                                <div className="flex justify-between text-xs">
                                    <span className="text-gray-600">招生期間管理</span>
                                    <span className="font-medium">
                                        <a href="/admin/recruitment-periods" className="text-blue-600 hover:underline">
                                            查看詳情
                                        </a>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                        <div>
                            <CardTitle className="flex items-center gap-2">
                                <Settings className="h-5 w-5" />
                                系統設定
                            </CardTitle>
                            <CardDescription>系統的基本功能、維護控制與資料同步設定</CardDescription>
                        </div>
                        <Button size="sm" onClick={handleSaveSettings} disabled={processing}>
                            <Save className="mr-1 h-3 w-3" />
                            <span className="text-xs">儲存設定</span>
                        </Button>
                    </CardHeader>

                    <CardContent className="space-y-8">
                        {/* 維護模式控制 */}
                        <div className="flex items-center justify-between rounded-lg border p-4">
                            <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                    {system_status.is_maintenance_mode ? (
                                        <PowerOff className="h-4 w-4 text-red-500" />
                                    ) : (
                                        <Power className="h-4 w-4 text-green-500" />
                                    )}
                                    <span className="font-medium">維護模式</span>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                    {system_status.is_maintenance_mode
                                        ? '系統目前處於維護模式，除管理員外其他用戶無法訪問'
                                        : '系統正常運行中，所有用戶都可以正常訪問'}
                                </p>
                            </div>
                            <Switch checked={system_status.is_maintenance_mode} onCheckedChange={toggleMaintenanceMode} disabled={isLoading} />
                        </div>

                        {/* 一般設定區塊 */}
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="reminder_cooldown">提醒冷卻時間 (小時)</Label>
                                    <Input
                                        id="reminder_cooldown"
                                        type="number"
                                        min="1"
                                        max="168"
                                        value={data.reminder_cooldown}
                                        onChange={(e) => setData('reminder_cooldown', e.target.value)}
                                    />
                                    <p className="text-xs text-muted-foreground">發送提醒後需等待的時間間隔</p>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="auto_timeout">自動超時天數</Label>
                                    <Input
                                        id="auto_timeout"
                                        type="number"
                                        min="1"
                                        max="30"
                                        value={data.auto_timeout}
                                        onChange={(e) => setData('auto_timeout', e.target.value)}
                                    />
                                    <p className="text-xs text-muted-foreground">推薦函自動標記為超時的天數</p>
                                </div>
                            </div>

                            {/* TODO: 推薦函上傳方式控制 */}
                            <div className="flex items-center">{/* TODO: 控制選項 */}</div>
                        </div>

                        {/* 外部資料同步 */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h4 className="font-medium">外部資料同步</h4>
                                    <p className="text-sm text-muted-foreground">從外部系統同步招生期間等設定資料</p>
                                </div>
                                <div className="flex gap-2">
                                    <Button variant="outline" size="sm" onClick={checkApiConnection} disabled={isCheckingApi}>
                                        {isCheckingApi ? (
                                            <RefreshCw className="h-4 w-4 animate-spin" />
                                        ) : apiStatus?.success ? (
                                            <Wifi className="h-4 w-4 text-green-500" />
                                        ) : (
                                            <WifiOff className="h-4 w-4 text-red-500" />
                                        )}
                                        檢查連線
                                    </Button>
                                    <Button variant="outline" size="sm" onClick={syncExternalData} disabled={isSyncing}>
                                        {isSyncing ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
                                        同步資料
                                    </Button>
                                </div>
                            </div>

                            {/* API 狀態訊息 */}
                            {apiStatus && (
                                <div
                                    className={`rounded-lg p-3 text-sm ${
                                        apiStatus.success
                                            ? 'border border-green-200 bg-green-50 text-green-700'
                                            : 'border border-red-200 bg-red-50 text-red-700'
                                    }`}
                                >
                                    {apiStatus.message}
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* 警告訊息 */}
                <Card className="border-orange-200 bg-orange-50">
                    <CardContent>
                        <div className="flex items-start gap-3">
                            <AlertTriangle className="mt-0.5 h-5 w-5 text-orange-600" />
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-orange-800">注意事項</p>
                                <p className="text-sm text-orange-700">
                                    修改系統設定可能會影響正在進行的申請流程，請謹慎操作。 建議在非招生期間進行重要設定的變更。
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
