<?php

namespace App\Services;

use App\Models\QuestionnaireTemplate;
use App\Models\RecommendationLetter;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use TCPDF;


/**
 * PDF 服務類
 * 
 * 處理推薦函系統中的 PDF 生成、合併和管理功能
 * 
 * 暫不提供問卷處理功能(僅開放推薦人上傳PDF檔案)
 */
class PdfService
{
    /**
     * 從問卷資料生成推薦函 PDF
     *
     * @param RecommendationLetter $recommendationLetter
     * @param array $questionnaireData
     * @return string PDF 內容
     */
    public function generateRecommendationPdf(RecommendationLetter $recommendationLetter, array $questionnaireData): string
    {
        // 取得問卷模板 - 先嘗試精確匹配
        $template = QuestionnaireTemplate::where('department_name', $recommendationLetter->department_name)
            ->where('program_type', $recommendationLetter->program_type)
            ->where('is_active', true)
            ->first();

        // 如果精確匹配失敗，嘗試按系所匹配第一個可用模板
        if (!$template) {
            $template = QuestionnaireTemplate::where('department_name', $recommendationLetter->department_name)
                ->where('is_active', true)
                ->first();
        }

        // 如果還是找不到，使用預設模板
        if (!$template) {
            $template = QuestionnaireTemplate::where('department_name', '通用')
                ->where('program_type', '一般')
                ->where('is_active', true)
                ->first();
        }

        if (!$template) {
            // 提供更詳細的錯誤訊息
            $availableTemplates = QuestionnaireTemplate::where('is_active', true)
                ->select('department_name', 'program_type', 'template_name')
                ->get();

            $errorMessage = sprintf(
                '找不到對應的問卷模板 (系所: %s, 學程: %s)。可用模板: %s',
                $recommendationLetter->department_name,
                $recommendationLetter->program_type,
                $availableTemplates->map(function ($t) {
                    return "{$t->department_name}-{$t->program_type}";
                })->join(', ') ?: '無'
            );

            throw new \Exception($errorMessage);
        }

        // 準備 PDF 資料
        $pdfData = [
            'recommendation' => $recommendationLetter,
            'questionnaire_data' => $questionnaireData,
            'template' => $template,
            'generated_at' => now(),
            'applicant' => $recommendationLetter->applicant,
            'recommender' => $recommendationLetter->recommender,
            // 添加模板需要的變數
            'applicant_name' => $recommendationLetter->applicant->user->name ?? 'N/A',
            'department_name' => $recommendationLetter->department_name,
            'program_type' => $recommendationLetter->program_type ?? 'master',
            'recommender_name' => $recommendationLetter->recommender_name,
            'recommender_title' => $recommendationLetter->recommender_title,
            'recommender_department' => $recommendationLetter->recommender_department,
            'recommender_email' => $recommendationLetter->recommender_email,
            'recommender_phone' => $recommendationLetter->recommender_phone,
        ];

        // 優先使用 TCPDF 處理中文編碼，失敗時回退到 DomPDF
        try {
            return $this->generatePdfWithTCPDF($recommendationLetter, $questionnaireData, $template, $pdfData);
        } catch (\Exception $e) {
            Log::warning('TCPDF 生成失敗，回退到 DomPDF', [
                'error' => $e->getMessage(),
                'recommendation_id' => $recommendationLetter->id
            ]);
        }

        // 回退方案：使用 DomPDF
        $pdf = Pdf::loadView('pdf.recommendation-letter', $pdfData);

        // 設定 PDF 選項，特別針對中文字體
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => false,
            'defaultFont' => 'DejaVu Sans',
            'enable_font_subsetting' => true,
            'isRemoteEnabled' => false,
            'defaultPaperSize' => 'A4',
            'defaultPaperOrientation' => 'portrait',
            'dpi' => 96,
            'font_height_ratio' => 1.1,
        ]);

        // 直接返回PDF內容，讓FileStorageService處理存儲
        return $pdf->output();
    }

    /**
     * 合併多個 PDF 檔案
     * 
     * @param array $pdfPaths PDF 檔案路徑陣列
     * @param string $outputFileName 輸出檔案名稱
     * @return string 合併後的 PDF 檔案路徑
     */
    public function mergePdfs(array $pdfPaths, string $outputFileName): string
    {
        // 這裡使用簡單的 PDF 合併邏輯
        // 實際專案中可能需要使用更專業的 PDF 處理庫如 TCPDF 或 FPDI

        $mergedContent = '';
        foreach ($pdfPaths as $path) {
            if (Storage::exists($path)) {
                $mergedContent .= Storage::get($path);
            }
        }

        $outputPath = "merged/{$outputFileName}";
        Storage::put($outputPath, $mergedContent);

        return $outputPath;
    }

    /**
     * 驗證 PDF 檔案
     *
     * @param string $filePath 檔案路徑（可以是實際檔案路徑或 Storage 路徑）
     * @return bool
     */
    public function validatePdf(string $filePath): bool
    {
        try {
            // 檢查是否為實際檔案路徑
            if (file_exists($filePath)) {
                $content = file_get_contents($filePath);
            } elseif (Storage::exists($filePath)) {
                // 檢查是否為 Storage 路徑
                $content = Storage::get($filePath);
            } else {
                Log::warning('PDF 驗證失敗：檔案不存在', ['file_path' => $filePath]);
                return false;
            }

            // 檢查檔案大小
            $fileSize = strlen($content);
            $maxSize = 10 * 1024 * 1024; // 10MB
            if ($fileSize > $maxSize) {
                Log::warning('PDF 驗證失敗：檔案過大', [
                    'file_path' => $filePath,
                    'file_size' => $fileSize,
                    'max_size' => $maxSize
                ]);
                return false;
            }

            // 檢查 PDF 檔案標頭
            $isValidPdf = str_starts_with($content, '%PDF-');

            if (!$isValidPdf) {
                Log::warning('PDF 驗證失敗：檔案格式無效', [
                    'file_path' => $filePath,
                    'file_header' => substr($content, 0, 10)
                ]);
            }

            return $isValidPdf;
        } catch (\Exception $e) {
            Log::error('PDF 驗證過程發生錯誤', [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 使用 TCPDF 生成支援中文的 PDF
     *
     * @param RecommendationLetter 推薦函
     * @param array 表單資料
     * @param QuestionnaireTemplate 模板
     * @return string PDF 檔案路徑
     */
    private function generatePdfWithTCPDF(RecommendationLetter $recommendationLetter, array $questionnaireData, QuestionnaireTemplate $template): string
    {
        // 創建 TCPDF 實例，強制使用 UTF-8
        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // 設定文件資訊
        $pdf->SetCreator('推薦函系統');
        $pdf->SetAuthor('推薦函系統');
        $pdf->SetTitle('推薦函 - ' . ($recommendationLetter->applicant->user->name ?? 'N/A'));
        $pdf->SetSubject('推薦函');

        // 設定頁面邊距
        $pdf->SetMargins(20, 20, 20);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(10);

        // 設定自動分頁
        $pdf->SetAutoPageBreak(true, 20);

        // 移除頁首和頁尾
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // 設定字體（使用支援中文的字體）
        // 嘗試使用不同的中文字體，優先使用繁體中文
        try {
            // 使用 TCPDF 內建的 CJK 字體
            $pdf->SetFont('cid0ct', '', 12); // 繁體中文
        } catch (\Exception $e) {
            try {
                $pdf->SetFont('cid0cs', '', 12); // 簡體中文
            } catch (\Exception $e2) {
                try {
                    $pdf->SetFont('arialunicid0', '', 12); // Arial Unicode
                } catch (\Exception $e3) {
                    $pdf->SetFont('dejavusans', '', 12); // DejaVu Sans
                }
            }
        }

        // 添加頁面
        $pdf->AddPage();

        // 生成 PDF 內容
        $html = $this->generateTCPDFContent($recommendationLetter, $questionnaireData, $template);

        // 寫入 HTML 內容
        $pdf->writeHTML($html, true, false, true, false, '');

        // 直接返回PDF內容，讓FileStorageService處理存儲
        return $pdf->Output('', 'S'); // 'S' 表示返回字串
    }

    /**
     * 生成 TCPDF 的 HTML 內容(推薦函模板 template)
     *
     * @param RecommendationLetter $recommendationLetter
     * @param array $questionnaireData
     * @param QuestionnaireTemplate $template
     * @return string
     */
    private function generateTCPDFContent(RecommendationLetter $recommendationLetter, array $questionnaireData, QuestionnaireTemplate $template): string
    {
        $applicantName = $recommendationLetter->applicant->user->name ?? 'N/A';
        $departmentName = $recommendationLetter->department_name;
        $programType = $recommendationLetter->program_type ?? 'N/A';
        $recommenderName = $recommendationLetter->recommender_name;
        $recommenderTitle = $recommendationLetter->recommender_title;
        $recommenderDepartment = $recommendationLetter->recommender_department;
        $recommenderEmail = $recommendationLetter->recommender_email;
        $recommenderPhone = $recommendationLetter->recommender_phone;
        $generatedAt = now();

        $html = '
        <style>
            body { font-family: "cid0ct", "cid0cs", "arialunicid0", "dejavusans", sans-serif; font-size: 12pt; line-height: 1.6; color: #333; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #2563eb; padding-bottom: 20px; }
            .header h1 { font-size: 24pt; font-weight: bold; color: #1e40af; margin: 0 0 10px 0; }
            .info-section { margin-bottom: 25px; background-color: #f8fafc; padding: 15px; border-left: 4px solid #2563eb; }
            .info-section h2 { font-size: 14pt; font-weight: bold; color: #1e40af; margin: 0 0 15px 0; }
            .info-row { margin-bottom: 8px; }
            .info-label { font-weight: bold; color: #374151; display: inline-block; width: 120px; }
            .info-value { color: #111827; display: inline; }
            .content-section { margin-bottom: 25px; }
            .content-section h3 { font-size: 14pt; font-weight: bold; color: #1e40af; margin: 0 0 15px 0; border-bottom: 1px solid #e5e7eb; padding-bottom: 5px; }
            .question-block { margin-bottom: 15px; }
            .question-inline { display: flex; align-items: flex-start; gap: 10px; }
            .question-label { font-weight: bold; color: #374151; min-width: 150px; flex-shrink: 0; }
            .question-answer { background-color: #f9fafb; padding: 8px 12px; border-left: 3px solid #3b82f6; flex: 1; }
            .question-answer-long { background-color: #f9fafb; padding: 12px; border-left: 3px solid #3b82f6; margin-top: 8px; }
            .signature-section { margin-top: 40px; text-align: right; }
            .footer { margin-top: 30px; text-align: center; font-size: 10pt; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 15px; }
        </style>

        <div class="header">
            <h1>推薦函</h1>
            <p class="subtitle">Letter of Recommendation</p>
        </div>

        <div class="info-section">
            <h2>基本資訊</h2>
            <div class="info-row">
                <span class="info-label">申請人姓名：</span>
                <span class="info-value">' . htmlspecialchars($applicantName) . '</span>
            </div>
            <div class="info-row">
                <span class="info-label">申請系所：</span>
                <span class="info-value">' . htmlspecialchars($departmentName) . '</span>
            </div>
            <div class="info-row">
                <span class="info-label">申請類別：</span>
                <span class="info-value">' . htmlspecialchars($programType) . '</span>
            </div>
        </div>

        <div class="info-section">
            <h2>推薦人資訊</h2>
            <div class="info-row">
                <span class="info-label">推薦人姓名：</span>
                <span class="info-value">' . htmlspecialchars($recommenderName) . '</span>
            </div>';

        if ($recommenderTitle) {
            $html .= '
            <div class="info-row">
                <span class="info-label">職稱：</span>
                <span class="info-value">' . htmlspecialchars($recommenderTitle) . '</span>
            </div>';
        }

        if ($recommenderDepartment) {
            $html .= '
            <div class="info-row">
                <span class="info-label">服務單位：</span>
                <span class="info-value">' . htmlspecialchars($recommenderDepartment) . '</span>
            </div>';
        }

        $html .= '
            <div class="info-row">
                <span class="info-label">聯絡電話：</span>
                <span class="info-value">' . htmlspecialchars($recommenderPhone ?? 'N/A') . '</span>
            </div>
            <div class="info-row">
                <span class="info-label">電子郵件：</span>
                <span class="info-value">' . htmlspecialchars($recommenderEmail) . '</span>
            </div>
        </div>';

        // 添加問卷內容
        if (!empty($questionnaireData)) {
            $html .= '<div class="content-section"><h3>推薦內容</h3>';

            foreach ($questionnaireData as $key => $value) {
                if (!empty($value)) {
                    $label = $this->getQuestionLabel($key, $template->questions);
                    $cleanValue = trim($value);

                    // 判斷內容長度和是否包含換行，決定使用內聯還是垂直布局
                    $isLongContent = strlen($cleanValue) > 100 || str_contains($cleanValue, "\n") || str_contains($cleanValue, "\r");

                    if ($isLongContent) {
                        // 長內容使用垂直布局
                        $html .= '
                        <div class="question-block">
                            <div class="question-label">' . htmlspecialchars($label) . '</div>
                            <div class="question-answer-long">' . nl2br(htmlspecialchars($cleanValue)) . '</div>
                        </div>';
                    } else {
                        // 短內容使用內聯布局
                        $html .= '
                        <div class="question-block">
                            <div class="question-inline">
                                <div class="question-label">' . htmlspecialchars($label) . '</div>
                                <div class="question-answer">' . htmlspecialchars($cleanValue) . '</div>
                            </div>
                        </div>';
                    }
                }
            }

            $html .= '</div>';
        }


        // 添加頁尾
        $html .= '
        <div class="footer">
            <p>此推薦函由系統自動生成於 ' . $generatedAt->format('Y年m月d日 H:i') . '</p>
        </div>';

        return $html;
    }

    /**
     * 取得問題標籤
     *
     * @param string $key
     * @return string
     */
    private function getQuestionLabel(string $key, array $questions): string
    {
        foreach ($questions as $question) {
            if ($question['id'] === $key) {
                return $question['label'];
            }
        }

        return $key;
    }

    /**
     * 合併多個PDF文件（用於資料合併作業）
     *
     * @param array $localFiles 本地PDF文件列表
     * @param array $externalFiles 外部PDF文件列表
     * @return string 合併後的PDF文件路徑
     */
    public function mergePdfFiles(array $localFiles, array $externalFiles): string
    {
        try {
            // 使用TCPDF合併PDF
            $pdf = new \TCPDF();
            $pdf->SetCreator('推薦函系統');
            $pdf->SetTitle('合併推薦函');
            $pdf->SetSubject('推薦函合併文件');

            // 設定字體
            try {
                $pdf->SetFont('cid0ct', '', 12);
            } catch (\Exception $e) {
                $pdf->SetFont('dejavusans', '', 12);
            }

            // 合併本地文件
            foreach ($localFiles as $file) {
                if (file_exists($file['file_path'])) {
                    $this->addPdfToMerge($pdf, $file['file_path'], "本地推薦函 - {$file['filename']}");
                }
            }

            // 合併外部文件
            foreach ($externalFiles as $file) {
                if (file_exists($file['temp_path'])) {
                    $this->addPdfToMerge($pdf, $file['temp_path'], "外部推薦函 - {$file['filename']}");
                }
            }

            // 生成合併後的文件路徑
            $mergedFileName = 'merged_' . uniqid() . '.pdf';
            $mergedPath = storage_path("app/temp/{$mergedFileName}");

            // 確保目錄存在
            if (!file_exists(dirname($mergedPath))) {
                mkdir(dirname($mergedPath), 0755, true);
            }

            // 保存合併後的PDF
            file_put_contents($mergedPath, $pdf->Output('', 'S'));

            Log::info('PDF合併完成', [
                'merged_path' => $mergedPath,
                'local_files' => count($localFiles),
                'external_files' => count($externalFiles)
            ]);

            return $mergedPath;
        } catch (\Exception $e) {
            Log::error('PDF合併失敗', [
                'error' => $e->getMessage(),
                'local_files' => count($localFiles),
                'external_files' => count($externalFiles)
            ]);

            throw new \Exception('PDF合併失敗: ' . $e->getMessage());
        }
    }

    /**
     * 將PDF文件添加到合併中
     */
    private function addPdfToMerge(\TCPDF $pdf, string $filePath, string $title): void
    {
        try {
            // 添加分頁標題
            $pdf->AddPage();
            $pdf->SetFont('cid0ct', 'B', 16);
            $pdf->Cell(0, 10, $title, 0, 1, 'C');
            $pdf->Ln(5);

            // 這裡應該使用PDF解析庫來讀取PDF內容
            // 由於TCPDF不直接支持PDF導入，我們使用簡化的方法
            $pdf->SetFont('cid0ct', '', 12);
            $pdf->Cell(0, 10, "PDF文件: " . basename($filePath), 0, 1);
            $pdf->Cell(0, 10, "文件大小: " . filesize($filePath) . " bytes", 0, 1);
            $pdf->Ln(10);

            // 注意：實際應用中應該使用專門的PDF合併庫如FPDI
            Log::info('PDF文件已添加到合併', ['file_path' => $filePath]);
        } catch (\Exception $e) {
            Log::warning('添加PDF文件到合併失敗', [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
    }
}
