<?php

require_once 'vendor/autoload.php';

use Illuminate\Http\Client\Factory as HttpFactory;

$http = new HttpFactory();

echo "測試增強的PDF合併功能和詳細日誌...\n\n";

try {
    // 1. 清理舊的日誌
    echo "1. 清理舊的日誌檔案\n";
    $logFile = 'storage/logs/laravel.log';
    if (file_exists($logFile)) {
        // 備份當前日誌
        $backupFile = 'storage/logs/laravel_backup_' . date('Y-m-d_H-i-s') . '.log';
        copy($logFile, $backupFile);
        echo "日誌已備份到: {$backupFile}\n";
        
        // 清空當前日誌
        file_put_contents($logFile, '');
        echo "日誌檔案已清空\n\n";
    }

    // 2. 測試API啟動合併任務
    echo "2. 啟動PDF合併任務\n";
    $response = $http->withHeaders([
        'User-Agent' => 'Laravel-RecommendationSystem',
        'Accept' => 'application/json',
        'Content-Type' => 'application/json'
    ])->withOptions([
        'verify' => false
    ])->post('https://rec-letter.test/api/pdf-merge/start-merge', [
        'exam_id' => '2',
        'exam_year' => 114
    ]);
    
    echo "狀態碼: " . $response->status() . "\n";
    echo "響應: " . $response->body() . "\n\n";
    
    $data = json_decode($response->body(), true);
    
    if (isset($data['task_id'])) {
        $taskId = $data['task_id'];
        echo "任務ID: {$taskId}\n\n";
        
        // 3. 手動處理隊列任務
        echo "3. 處理隊列任務\n";
        $output = shell_exec('php artisan queue:work --once --timeout=120 2>&1');
        echo "隊列輸出: {$output}\n\n";
        
        // 4. 檢查任務狀態
        echo "4. 檢查任務狀態\n";
        $statusResponse = $http->withHeaders([
            'User-Agent' => 'Laravel-RecommendationSystem',
            'Accept' => 'application/json'
        ])->withOptions([
            'verify' => false
        ])->get("https://rec-letter.test/api/pdf-merge/merge-status?task_id={$taskId}");
        
        echo "狀態響應: " . $statusResponse->body() . "\n\n";
        
        // 5. 分析日誌
        echo "5. 分析處理日誌\n";
        if (file_exists($logFile)) {
            $logContent = file_get_contents($logFile);
            $logLines = explode("\n", $logContent);
            
            echo "=== PDF合併處理日誌分析 ===\n";
            
            // 查找關鍵日誌條目
            $relevantLogs = [];
            foreach ($logLines as $line) {
                if (strpos($line, 'PDF合併') !== false || 
                    strpos($line, 'FPDI') !== false || 
                    strpos($line, 'TCPDF') !== false || 
                    strpos($line, 'DomPDF') !== false ||
                    strpos($line, '嘗試') !== false ||
                    strpos($line, '成功完成') !== false ||
                    strpos($line, '失敗') !== false) {
                    $relevantLogs[] = $line;
                }
            }
            
            if (!empty($relevantLogs)) {
                foreach ($relevantLogs as $log) {
                    echo $log . "\n";
                }
            } else {
                echo "沒有找到相關的PDF合併日誌\n";
            }
            
            echo "\n=== 日誌統計 ===\n";
            echo "總日誌行數: " . count($logLines) . "\n";
            echo "相關日誌行數: " . count($relevantLogs) . "\n";
            
            // 檢查使用的合併方法
            $fpdiLogs = array_filter($relevantLogs, fn($log) => strpos($log, 'FPDI') !== false);
            $tcpdfLogs = array_filter($relevantLogs, fn($log) => strpos($log, 'TCPDF') !== false);
            $dompdfLogs = array_filter($relevantLogs, fn($log) => strpos($log, 'DomPDF') !== false);
            
            echo "FPDI相關日誌: " . count($fpdiLogs) . " 條\n";
            echo "TCPDF相關日誌: " . count($tcpdfLogs) . " 條\n";
            echo "DomPDF相關日誌: " . count($dompdfLogs) . " 條\n";
            
            // 檢查成功/失敗狀態
            $successLogs = array_filter($relevantLogs, fn($log) => strpos($log, '成功完成') !== false);
            $failLogs = array_filter($relevantLogs, fn($log) => strpos($log, '失敗') !== false);
            
            echo "成功日誌: " . count($successLogs) . " 條\n";
            echo "失敗日誌: " . count($failLogs) . " 條\n\n";
            
            // 判斷使用的方法
            if (count($fpdiLogs) > 0 && count($successLogs) > 0) {
                echo "✅ 使用FPDI方法成功合併PDF\n";
            } elseif (count($tcpdfLogs) > 0 && count($successLogs) > 0) {
                echo "⚠️ 使用TCPDF備用方法合併PDF\n";
            } elseif (count($dompdfLogs) > 0 && count($successLogs) > 0) {
                echo "⚠️ 使用DomPDF最後備用方法\n";
            } else {
                echo "❌ PDF合併可能失敗\n";
            }
        } else {
            echo "日誌檔案不存在\n";
        }
        
        // 6. 檢查生成的檔案
        echo "\n6. 檢查生成的檔案\n";
        $mergedDir = 'storage/app/temp/merged';
        if (is_dir($mergedDir)) {
            $files = scandir($mergedDir);
            echo "合併目錄中的檔案:\n";
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..') {
                    $filePath = $mergedDir . DIRECTORY_SEPARATOR . $file;
                    $size = filesize($filePath);
                    echo "- {$file} ({$size} bytes)\n";
                    
                    // 檢查是否使用autono格式命名
                    if (preg_match('/^\d+\.pdf$/', $file)) {
                        echo "  ✓ 使用autono格式命名\n";
                    } else {
                        echo "  ✗ 未使用autono格式命名\n";
                    }
                    
                    // 檢查PDF檔案是否有效
                    if ($size > 1000) { // 大於1KB可能是有效的PDF
                        echo "  ✓ 檔案大小正常\n";
                    } else {
                        echo "  ⚠️ 檔案大小可能異常\n";
                    }
                }
            }
        } else {
            echo "合併目錄不存在\n";
        }
    }
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
}

echo "\n測試完成\n";
