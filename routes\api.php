<?php

use App\Http\Controllers\Auth\ApplicantLoginController;
use App\Http\Controllers\Api\RecommendationApiController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API 路由
|--------------------------------------------------------------------------
|
| 注意：Laravel 會自動為 api.php 添加 /api 前綴
|
| 已實作API白名單中間件，僅允許來自授權系統的請求
| 因推薦函資料位於此系統，所以須開放給API系統索取資料
*/

/**
 * 報名系統考生登入
 *
 * 將從報名系統收到的 token 送去 API系統 進行解碼和驗證
 * 取得考生資料後登入後建立或更新考生，並登入系統
 */
Route::get('/auth-from-external', [ApplicantLoginController::class, 'handleExternalAuth'])
    ->middleware(['web', 'api.whitelist'])
    ->name('api.auth.external');

/**
 * 受保護的API路由群組
 *
 * 需要通過API白名單驗證才能訪問
 */
Route::middleware(['api.whitelist'])->group(function () {

    /** 健康檢查 - 檢查API服務狀態 */
    Route::get('/health', [RecommendationApiController::class, 'health'])
        ->name('api.health');

    /** API資訊 - 取得API版本和端點資訊 */
    Route::get('/info', [RecommendationApiController::class, 'info'])
        ->name('api.info');

    /** 推薦函相關API */
    Route::prefix('recommendations')->name('api.recommendations.')->group(function () {

        /** 根據考生資訊查詢推薦函 */
        Route::get('/by-applicant', [RecommendationApiController::class, 'getRecommendationsByApplicant'])
            ->name('by-applicant');

        /** 查詢推薦函統計資料 */
        Route::get('/stats', [RecommendationApiController::class, 'getRecommendationStats'])
            ->name('stats');
    });
});
