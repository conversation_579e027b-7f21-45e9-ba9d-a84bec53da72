import AdminLayout from '@/layouts/admin-layout';
import { <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, Activity, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { useState } from 'react';
import StatusBadge from '@/components/admin/StatusBadge';
import SearchAndFilter from '@/components/admin/SearchAndFilter';
import DataTable from '@/components/admin/DataTable';
import StatsCards from '@/components/admin/StatsCards';

// {
//     "id": 74,
//     "type": "operation",
//     "action": "view",
//     "description": "查看系統日誌",
//     "user_id": 1,
//     "ip_address": "127.0.0.1",
//     "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
//     "metadata": {
//         "filters": [],
//         "page": 1,
//         "per_page": 20,
//         "user_role": "admin",
//         "user_name": "Admin User"
//     },
//     "level": "info",
//     "created_at": "2025-07-22T05:08:08.000000Z",
//     "updated_at": "2025-07-22T05:08:08.000000Z",
//     "user": {
//         "id": 1,
//         "name": "Admin User",
//         "email": "<EMAIL>",
//         "role": "admin"
//     }
// }
interface SystemLog {
    id: number;
    user_id?: number;
    action: string;
    description: string;
    level: string;
    ip_address: string;
    user_agent: string;
    context?: any;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface SystemLogsProps {
    logs: {
        data: SystemLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function SystemLogs({ logs, log_types, log_levels, statistics, filters }: SystemLogsProps) {
    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統日誌', href: '/admin/system-logs' },
    ];

    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [levelFilter, setLevelFilter] = useState('all');

    console.log('System Logs:', logs.data);
    console.log('Log Types:', log_types);
    console.log('Log Levels:', log_levels);
    console.log('Statistics:', statistics);
    console.log('Filters:', filters);

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="系統日誌" description="查看和管理系統操作記錄和錯誤日誌">
            <Head title="系統日誌" />

            <div className="space-y-6 p-6">
                <SearchAndFilter searchTerm={searchTerm} onSearchChange={setSearchTerm} searchPlaceholder="搜尋日誌..." />

                <StatsCards stats={[]} />

                <DataTable data={logs.data} columns={[]} title="系統日誌列表" emptyMessage="沒有找到符合條件的日誌" />
            </div>
        </AdminLayout>
    );
}
