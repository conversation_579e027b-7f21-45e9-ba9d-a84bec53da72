
[2025-07-25 10:36:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/health","method":"GET"} 
[2025-07-25 10:36:24] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 10:36:24] local.ERROR: 啟動PDF合併任務失敗 {"error":"SQLSTATE[HY000]: General error: 1 no such table: jobs (Connection: sqlite, SQL: insert into \"jobs\" (\"queue\", \"attempts\", \"reserved_at\", \"available_at\", \"created_at\", \"payload\") values (default, 0, ?, **********, **********, {\"uuid\":\"99fd663d-f9b9-4f32-8db1-f20c1da5c84b\",\"displayName\":\"App\\\\Jobs\\\\ProcessPdfMergeJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":1800,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ProcessPdfMergeJob\",\"command\":\"O:27:\\\"App\\\\Jobs\\\\ProcessPdfMergeJob\\\":2:{s:9:\\\"\\u0000*\\u0000taskId\\\";s:33:\\\"merge_4rRsUFgr4d5G4ji6_**********\\\";s:13:\\\"\\u0000*\\u0000parameters\\\";a:4:{s:7:\\\"exam_id\\\";s:4:\\\"test\\\";s:9:\\\"exam_year\\\";i:2024;s:12:\\\"requested_at\\\";s:27:\\\"2025-07-25T02:36:24.190369Z\\\";s:9:\\\"client_ip\\\";s:9:\\\"127.0.0.1\\\";}}\"},\"createdAt\":**********,\"delay\":null}))","request_data":{"exam_id":"test","exam_year":2024},"client_ip":"127.0.0.1"} 
[2025-07-25 10:40:56] local.ERROR: SQLSTATE[HY000]: General error: 1 table "pdf_merge_tasks" already exists (Connection: sqlite, SQL: create table "pdf_merge_tasks" ("id" integer primary key autoincrement not null, "created_at" datetime, "updated_at" datetime)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists (Connection: sqlite, SQL: create table \"pdf_merge_tasks\" (\"id\" integer primary key autoincrement not null, \"created_at\" datetime, \"updated_at\" datetime)) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_07_25_103155_create_pdf_merge_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_25_1031...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_25_1031...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"p...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"p...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_07_25_103155_create_pdf_merge_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_25_1031...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_25_1031...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-25 10:41:04] local.ERROR: SQLSTATE[HY000]: General error: 1 table "pdf_merge_tasks" already exists (Connection: sqlite, SQL: create table "pdf_merge_tasks" ("id" integer primary key autoincrement not null, "created_at" datetime, "updated_at" datetime)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists (Connection: sqlite, SQL: create table \"pdf_merge_tasks\" (\"id\" integer primary key autoincrement not null, \"created_at\" datetime, \"updated_at\" datetime)) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_07_25_103155_create_pdf_merge_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_25_1031...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_25_1031...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"p...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"p...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_07_25_103155_create_pdf_merge_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_25_1031...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_25_1031...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-25 10:41:44] local.ERROR: SQLSTATE[HY000]: General error: 1 table "pdf_merge_tasks" already exists (Connection: sqlite, SQL: create table "pdf_merge_tasks" ("id" integer primary key autoincrement not null, "task_id" varchar not null, "status" varchar check ("status" in ('processing', 'ready', 'failed', 'expired')) not null default 'processing', "progress" integer not null default '0', "parameters" text, "download_url" varchar, "zip_file_path" varchar, "total_files" integer not null default '0', "processed_files" integer not null default '0', "error_message" text, "expires_at" datetime, "created_at" datetime, "updated_at" datetime)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists (Connection: sqlite, SQL: create table \"pdf_merge_tasks\" (\"id\" integer primary key autoincrement not null, \"task_id\" varchar not null, \"status\" varchar check (\"status\" in ('processing', 'ready', 'failed', 'expired')) not null default 'processing', \"progress\" integer not null default '0', \"parameters\" text, \"download_url\" varchar, \"zip_file_path\" varchar, \"total_files\" integer not null default '0', \"processed_files\" integer not null default '0', \"error_message\" text, \"expires_at\" datetime, \"created_at\" datetime, \"updated_at\" datetime)) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_06_24_000001_create_recommendation_system_tables.php(270): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_24_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_24_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"p...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"p...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_06_24_000001_create_recommendation_system_tables.php(270): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_24_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_24_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}
"} 
[2025-07-25 10:42:44] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-25 10:42:51] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T02:42:51.644022Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T02:42:51.645781Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T02:42:51.645917Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T02:42:51.645997Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T02:42:51.646069Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T02:42:51.646137Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T02:42:51.646206Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T02:42:51.646275Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T02:42:51.646343Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T02:42:51.646440Z"}]} 
[2025-07-25 10:42:51] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T02:42:51.644022Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T02:42:51.645781Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T02:42:51.645917Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T02:42:51.645997Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T02:42:51.646069Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T02:42:51.646137Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T02:42:51.646206Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T02:42:51.646275Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T02:42:51.646343Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T02:42:51.646440Z"}]}}}} 
[2025-07-25 10:48:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 10:48:12] local.INFO: PDF合併任務已啟動 {"task_id":"merge_aVSEus5bLuJhAFnE_1753411692","parameters":{"requested_at":"2025-07-25T02:48:12.028980Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 10:49:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 10:50:22] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 10:50:22] local.INFO: PDF合併任務已啟動 {"task_id":"merge_ZxSiS5LXc71iKnYT_1753411822","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T02:50:22.889541Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 10:53:03] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 10:56:51] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 10:58:46] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-25 10:58:53] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T02:58:53.208762Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T02:58:53.210391Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T02:58:53.210482Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T02:58:53.210556Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T02:58:53.210623Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T02:58:53.210686Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T02:58:53.210749Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T02:58:53.210809Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T02:58:53.210870Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T02:58:53.210930Z"}]} 
[2025-07-25 10:58:53] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T02:58:53.208762Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T02:58:53.210391Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T02:58:53.210482Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T02:58:53.210556Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T02:58:53.210623Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T02:58:53.210686Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T02:58:53.210749Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T02:58:53.210809Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T02:58:53.210870Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T02:58:53.210930Z"}]}}}} 
[2025-07-25 10:58:56] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 10:59:16] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 10:59:23] local.INFO: 建立新使用者帳號 {"user_id":2,"email":"<EMAIL>","role":"applicant"} 
[2025-07-25 10:59:23] local.INFO: 建立新考生記錄 {"user_id":2,"applicant_id":1,"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":"114","exam_id":"2"} 
[2025-07-25 10:59:23] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 10:59:24] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 10:59:53] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
