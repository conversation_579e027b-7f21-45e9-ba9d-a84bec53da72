# 提交管理重構總結

## 重構概述

本次重構將原本分散的PDF相關功能與問卷相關功能合併，統一歸類為Submission（提交管理）功能，並新增了資料合併作業功能。

## 主要變更

### 1. 控制器合併

**原有控制器：**
- `App\Http\Controllers\Admin\PdfManagementController`
- `App\Http\Controllers\Admin\QuestionnaireController`

**新控制器：**
- `App\Http\Controllers\Admin\SubmissionController`

**功能整合：**
- 問卷模板管理（CSV上傳、創建、編輯、刪除）
- 提交方式設定（PDF上傳、問卷填寫開關）
- 資料合併作業（新功能）

### 2. 路由重構

**原有路由：**
```php
// PDF管理
Route::prefix('pdf-management')->name('pdf-management.')->group(function () {
    Route::get('/', [PdfManagementController::class, 'index']);
    // ...
});

// 問卷模板管理
Route::prefix('questionnaire-templates')->name('questionnaire-templates.')->group(function () {
    Route::get('/', [QuestionnaireController::class, 'index']);
    // ...
});
```

**新路由：**
```php
// 統一的提交管理
Route::prefix('submission')->name('submission.')->group(function () {
    Route::get('/', [SubmissionController::class, 'index']);
    Route::post('/submission-settings', [SubmissionController::class, 'updateSubmissionSettings']);
    Route::post('/upload-csv', [SubmissionController::class, 'uploadCsvTemplate']);
    Route::post('/start-merge', [SubmissionController::class, 'startDataMerge']);
    // ...
});
```

### 3. 前端頁面整合

**原有頁面：**
- `resources/js/pages/admin/pdf-management.tsx`
- `resources/js/pages/admin/questionnaire-templates.tsx`

**新頁面：**
- `resources/js/pages/admin/submission-management.tsx`

**功能標籤頁：**
1. **問卷模板** - 管理問卷模板，支援CSV上傳
2. **提交設定** - 設定允許的提交方式
3. **資料合併** - 新增的資料合併作業功能

### 4. 新增服務

**DataMergeService (`app/Services/DataMergeService.php`)**
- 檢查外部系統條件
- 從外部系統索取文件
- 合併本地和外部PDF文件
- 生成壓縮檔

**PdfService 擴展**
- 新增 `mergePdfFiles()` 方法用於PDF合併

### 5. 導航更新

**側邊欄導航：**
- 原：「上傳方式管理」→ 新：「提交管理」
- 路由：`/admin/pdf-management` → `/admin/submission`

## 新功能：資料合併作業

### 功能描述
向外部系統確認特定條件是否滿足，若滿足則索取相關文件回系統後進行關聯的PDF合併，將兩個系統的考生推薦函合併並產生壓縮檔。

### 實現架構
1. **條件檢查** - 向外部系統API發送條件查詢
2. **文件索取** - 從外部系統下載相關推薦函文件
3. **PDF合併** - 將本地和外部文件合併為單一PDF
4. **壓縮打包** - 生成包含所有合併文件的ZIP檔案
5. **下載提供** - 提供合併結果的下載功能

### API端點
- `POST /admin/submission/start-merge` - 開始資料合併作業
- `GET /admin/submission/download-merge/{filename}` - 下載合併結果

### 配置設定
新增外部API配置 (`config/recommendation.php`)：
```php
'external_api' => [
    'base_url' => env('EXTERNAL_API_BASE_URL', 'http://localhost:18002'),
    'timeout' => env('EXTERNAL_API_TIMEOUT', 30),
    // ...
]
```

## 後台功能整合

### 問卷維護
- **CSV上傳** - 批量創建問卷模板
- **模板管理** - 啟用/停用、編輯、刪除問卷模板
- **系所篩選** - 按系所篩選模板列表

### 提交方式設定
- **PDF上傳開關** - 控制是否允許推薦人上傳PDF
- **問卷填寫開關** - 控制是否允許推薦人填寫線上問卷
- **系統資訊顯示** - 顯示上傳限制、存儲路徑等資訊

## 測試覆蓋

創建了完整的功能測試 (`tests/Feature/SubmissionControllerTest.php`)：
- 頁面載入測試
- 提交設定更新測試
- CSV模板上傳測試
- 模板狀態切換測試
- 權限控制測試

## 向後兼容性

### 保留的功能
- 所有原有的問卷模板管理功能
- 提交方式設定功能
- 系統設定和日誌記錄

### 移除的功能
- 獨立的PDF模板管理（已整合到問卷模板）
- 分散的設定頁面（已統一到提交管理）

## 部署注意事項

1. **路由更新** - 需要更新任何硬編碼的路由引用
2. **權限檢查** - 確保管理員權限正確配置
3. **外部API配置** - 設定外部系統API相關環境變數
4. **存儲目錄** - 確保 `storage/app/temp` 目錄存在且可寫

## 未來擴展

### 資料合併作業
- 外部系統API實際對接
- 更完善的PDF合併功能（使用FPDI等專業庫）
- 合併作業歷史記錄
- 批次處理和進度追蹤

### 問卷模板
- 模板版本控制
- 模板預覽功能
- 更豐富的問題類型支援

## 總結

本次重構成功將分散的功能整合為統一的提交管理系統，提供了更好的用戶體驗和維護性。新增的資料合併作業功能為系統間的數據整合提供了基礎架構，雖然外部系統尚未實作，但UI介面和大架構已經完成，為未來的功能擴展奠定了良好基礎。
